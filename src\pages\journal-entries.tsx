import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function JournalEntries() {
  const [showAddModal, setShowAddModal] = useState(false);
  const [formData, setFormData] = useState({
    accountId: "",
    date: new Date().toISOString().split('T')[0],
    description: "",
    debit: 0,
    credit: 0,
    reference: "",
    transactionType: "Sales",
    departmentId: "",
    costCenterId: ""
  });

  const journalEntries = useQuery(api.tasks.getJournalEntries);
  const accounts = useQuery(api.tasks.getAccounts);
  const addJournalEntry = useMutation(api.tasks.addJournalEntry);
  const deleteJournalEntry = useMutation(api.tasks.deleteJournalEntry);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.debit === 0 && formData.credit === 0) {
      alert("Please enter either a debit or credit amount");
      return;
    }
    
    if (formData.debit > 0 && formData.credit > 0) {
      alert("Please enter either debit OR credit, not both");
      return;
    }

    try {
      await addJournalEntry({
        ...formData,
        accountId: formData.accountId as any
      });
      setShowAddModal(false);
      setFormData({
        accountId: "",
        date: new Date().toISOString().split('T')[0],
        description: "",
        debit: 0,
        credit: 0,
        reference: "",
        transactionType: "Sales",
        departmentId: "",
        costCenterId: ""
      });
    } catch (error) {
      alert("Error saving journal entry: " + error);
    }
  };

  const handleDelete = async (entryId: string) => {
    if (confirm("Are you sure you want to delete this journal entry?")) {
      try {
        await deleteJournalEntry({ id: entryId });
      } catch (error) {
        alert("Error deleting journal entry: " + error);
      }
    }
  };

  if (!journalEntries || !accounts) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Journal Entries</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  const getAccountName = (accountId: string) => {
    const account = accounts.find(acc => acc._id === accountId);
    return account ? `${account.code} - ${account.name}` : 'Unknown Account';
  };

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Journal Entries</h1>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Entry
            </button>
          </div>

          {/* Journal Entries Table */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Account</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Description</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">Debit</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Reference</th>
                    <th className="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {journalEntries.map((entry) => (
                    <tr key={entry._id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">{new Date(entry.date).toLocaleDateString()}</td>
                      <td className="py-4 px-4">
                        <div className="text-sm font-medium">{getAccountName(entry.accountId)}</div>
                        <div className="text-xs text-gray-500">{entry.transactionType}</div>
                      </td>
                      <td className="py-4 px-4">{entry.description}</td>
                      <td className="py-4 px-4 text-right">
                        {entry.debit > 0 ? (
                          <span className="text-red-600 font-medium">${entry.debit.toLocaleString()}</span>
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                      <td className="py-4 px-4 text-right">
                        {entry.credit > 0 ? (
                          <span className="text-green-600 font-medium">${entry.credit.toLocaleString()}</span>
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                      <td className="py-4 px-4">{entry.reference || '-'}</td>
                      <td className="py-4 px-4 text-center">
                        <button
                          onClick={() => handleDelete(entry._id)}
                          className="text-red-600 hover:text-red-800 text-sm font-medium"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Add Modal */}
          {showAddModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-lg">
                <h3 className="text-lg font-semibold mb-4">Add Journal Entry</h3>
                <form onSubmit={handleSubmit}>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Account
                      </label>
                      <select
                        value={formData.accountId}
                        onChange={(e) => setFormData({...formData, accountId: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="">Select Account</option>
                        {accounts.map((account) => (
                          <option key={account._id} value={account._id}>
                            {account.code} - {account.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date
                      </label>
                      <input
                        type="date"
                        value={formData.date}
                        onChange={(e) => setFormData({...formData, date: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <input
                        type="text"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Debit Amount
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={formData.debit}
                          onChange={(e) => setFormData({...formData, debit: Number(e.target.value), credit: 0})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Credit Amount
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={formData.credit}
                          onChange={(e) => setFormData({...formData, credit: Number(e.target.value), debit: 0})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Reference
                        </label>
                        <input
                          type="text"
                          value={formData.reference}
                          onChange={(e) => setFormData({...formData, reference: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Invoice #, PO #, etc."
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Transaction Type
                        </label>
                        <select
                          value={formData.transactionType}
                          onChange={(e) => setFormData({...formData, transactionType: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="Sales">Sales</option>
                          <option value="Purchase">Purchase</option>
                          <option value="Production">Production</option>
                          <option value="Payroll">Payroll</option>
                          <option value="Depreciation">Depreciation</option>
                          <option value="Adjustment">Adjustment</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      type="button"
                      onClick={() => {
                        setShowAddModal(false);
                        setFormData({
                          accountId: "",
                          date: new Date().toISOString().split('T')[0],
                          description: "",
                          debit: 0,
                          credit: 0,
                          reference: "",
                          transactionType: "Sales",
                          departmentId: "",
                          costCenterId: ""
                        });
                      }}
                      className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Add Entry
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

export default JournalEntries;
