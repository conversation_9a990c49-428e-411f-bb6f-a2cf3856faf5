import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import {
  Home,
  FileText,
  Bell,
  Settings,
  LogOut,
  Users,
  BarChart,
  Clock,
  BookOpen,
} from "lucide-react";
 import logoIcon from "../images/lapuoslogo.png";

const DynamicSidebar = () => {
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 768);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => setIsSmallScreen(window.innerWidth < 768);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleLogout = () => {
    localStorage.clear();
    sessionStorage.clear();
    navigate("/");
  };

  const navItems = [
    { name: "Dashboard", icon: <Home />, route: "/" },
    { name: "Accounts Payable", icon: <FileText />, route: "/accounts-payable" },
    { name: "Accounts Receivable", icon: <Clock />, route: "/accounts-receivable" },
    { name: "Chart of Accounts", icon: <BookOpen />, route: "/chart-of-accounts" },
    { name: "Account Schedules", icon: <FileText />, route: "/schedules" },
    { name: "Customers", icon: <Users />, route: "/customers" },
    { name: "Suppliers", icon: <Users />, route: "/suppliers" },
    { name: "Financial Reports", icon: <BarChart />, route: "/reports" },
    { name: "Notifications", icon: <Bell />, route: "/notifications" },
    { name: "Settings", icon: <Settings />, route: "/settings" },
  ];

  return (
    <aside
      className={`bg-[#0A192F] text-white transition-all duration-300 ease-in-out ${
        isSmallScreen ? "w-20" : "w-64"
      } sticky top-0 h-screen flex flex-col`}
    >
      {/* Logo - Fixed at top */}
      <div className="flex-shrink-0">
        <div className={`flex items-center p-4 space-x-3 text-lg font-bold border-b border-gray-700 ${
          isSmallScreen ? 'justify-center' : ''
        }`}>
           <img src={logoIcon} alt="App Logo" className="h-10 w-8 flex-shrink-0" />
          {!isSmallScreen && (
            <h1 className="text-teal-400 truncate">
             Finance <span className="text-white">ERP</span>
            </h1>
          )}
        </div>
      </div>

      {/* Navigation - Scrollable */}
      <div className="flex-1 overflow-hidden">
        <nav className="h-full overflow-y-auto px-2 py-4 space-y-1 scrollbar-thin">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.route}
              className={`flex items-center px-3 py-2.5 space-x-3 transition-all rounded-lg hover:bg-teal-400 group text-sm ${
                isSmallScreen ? 'justify-center' : ''
              }`}
              title={isSmallScreen ? item.name : undefined}
            >
              <span className="text-gray-100 flex-shrink-0 w-5 h-5">{item.icon}</span>
              {!isSmallScreen && (
                <span className="truncate group-hover:text-white font-medium">
                  {item.name}
                </span>
              )}
            </Link>
          ))}
        </nav>
      </div>

      {/* Logout - Fixed at bottom */}
      <div className="flex-shrink-0 border-t border-gray-700 p-4">
        <button
          type="button"
          onClick={handleLogout}
          className={`flex items-center space-x-3 text-red-300 hover:text-red-500 transition-colors w-full px-3 py-2.5 rounded-lg hover:bg-red-900/20 text-sm ${
            isSmallScreen ? 'justify-center' : ''
          }`}
          title={isSmallScreen ? "Sign Out" : undefined}
        >
          <LogOut className="flex-shrink-0 w-5 h-5" />
          {!isSmallScreen && <span className="truncate font-medium">Sign Out</span>}
        </button>
      </div>
    </aside>
  );
};

export default DynamicSidebar;
