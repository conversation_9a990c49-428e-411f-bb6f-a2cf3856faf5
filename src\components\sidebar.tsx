import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import {
  Home,
  FileText,
  Bell,
  Settings,
  LogOut,
  Users,
  BarChart,
  Clock,
  BookOpen,
} from "lucide-react";
 import logoIcon from "../images/lapuoslogo.png";

const DynamicSidebar = () => {
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 768);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => setIsSmallScreen(window.innerWidth < 768);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleLogout = () => {
    localStorage.clear();
    sessionStorage.clear();
    navigate("/");
  };

  const navItems = [
    { name: "Dashboard", icon: <Home />, route: "/" },
    { name: "Accounts Payable", icon: <FileText />, route: "/accounts-payable" },
    { name: "Accounts Receivable", icon: <Clock />, route: "/accounts-receivable" },
    { name: "Chart of Accounts", icon: <BookOpen />, route: "/chart-of-accounts" },
    { name: "Customers", icon: <Users />, route: "/customers" },
    { name: "Suppliers", icon: <Users />, route: "/suppliers" },
    { name: "Financial Reports", icon: <BarChart />, route: "/reports" },
    { name: "Notifications", icon: <Bell />, route: "/notifications" },
    { name: "Settings", icon: <Settings />, route: "/settings" },
  ];

  return (
    <aside
      className={`bg-[#0A192F] text-white transition-all duration-300 ease-in-out ${
        isSmallScreen ? "w-20" : "w-64"
      } sticky top-0 h-screen flex flex-col justify-between`}
    >
      {/* Logo */}
      <div>
        <div className="flex items-center p-5 space-x-3 text-xl font-bold">
           <img src={logoIcon} alt="App Logo" className=" h-8" />
          {!isSmallScreen && (
            <h1 className="text-teal-400">
             Finance <span className="text-white"> ERP</span>
            </h1>
          )}
        </div>

        {/* Navigation */}
        <nav className="flex flex-col mt-6 space-y-2 overflow-y-auto px-2">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.route}
              className="flex items-center px-4 py-3 space-x-3 transition-all rounded-lg hover:bg-teal-400"
            >
              <span className="text-gray-100">{item.icon}</span>
              {!isSmallScreen && <span>{item.name}</span>}
            </Link>
          ))}
        </nav>
      </div>

      {/* Logout */}
      <div className="px-4 py-5">
        <button
          type="button"
          onClick={handleLogout}
          className="flex items-center space-x-3 text-red-300 hover:text-red-500"
        >
          <LogOut />
          {!isSmallScreen && <span>Sign Out</span>}
        </button>
      </div>
    </aside>
  );
};

export default DynamicSidebar;
