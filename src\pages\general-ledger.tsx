import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function GeneralLedger() {
  const [selectedAccountId, setSelectedAccountId] = useState<string>("");
  const ledgerData = useQuery(api.tasks.getGeneralLedger);
  const accounts = useQuery(api.tasks.getAccounts);

  if (!ledgerData || !accounts) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">General <PERSON>ger</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  const selectedLedger = selectedAccountId 
    ? ledgerData.find(ledger => ledger.account._id === selectedAccountId)
    : null;

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">General Ledger</h1>
            <div className="text-sm text-gray-600">
              As of {new Date().toLocaleDateString()}
            </div>
          </div>

          {/* Account Selector */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Account to View Ledger
            </label>
            <select
              value={selectedAccountId}
              onChange={(e) => setSelectedAccountId(e.target.value)}
              className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Choose an account...</option>
              {accounts
                .filter(account => ledgerData.some(ledger => ledger.account._id === account._id))
                .map((account) => (
                  <option key={account._id} value={account._id}>
                    {account.code} - {account.name}
                  </option>
                ))}
            </select>
          </div>

          {!selectedLedger ? (
            /* Account Summary Cards */
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {ledgerData.map((ledger) => (
                <div 
                  key={ledger.account._id} 
                  className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 cursor-pointer hover:shadow-xl transition-shadow"
                  onClick={() => setSelectedAccountId(ledger.account._id)}
                >
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{ledger.account.name}</h3>
                      <p className="text-sm text-gray-500">{ledger.account.code}</p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      ledger.account.type === 'Asset' ? 'bg-blue-100 text-blue-800' :
                      ledger.account.type === 'Liability' ? 'bg-red-100 text-red-800' :
                      ledger.account.type === 'Equity' ? 'bg-purple-100 text-purple-800' :
                      ledger.account.type === 'Revenue' ? 'bg-green-100 text-green-800' :
                      'bg-orange-100 text-orange-800'
                    }`}>
                      {ledger.account.type}
                    </span>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Entries:</span>
                      <span className="text-sm font-medium">{ledger.entries.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Debits:</span>
                      <span className="text-sm font-medium">${ledger.totalDebits.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Credits:</span>
                      <span className="text-sm font-medium">${ledger.totalCredits.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-sm font-semibold text-gray-700">Balance:</span>
                      <span className={`text-sm font-bold ${
                        ledger.finalBalance >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        ${Math.abs(ledger.finalBalance).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* Selected Account Ledger */
            <div className="space-y-6">
              {/* Account Header */}
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">{selectedLedger.account.name}</h2>
                    <p className="text-gray-600">Account Code: {selectedLedger.account.code}</p>
                    <p className="text-gray-600">Account Type: {selectedLedger.account.type}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-600">Current Balance</div>
                    <div className={`text-3xl font-bold ${
                      selectedLedger.finalBalance >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      ${Math.abs(selectedLedger.finalBalance).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Ledger Entries */}
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Transaction History</h3>
                  <button
                    onClick={() => setSelectedAccountId("")}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    ← Back to All Accounts
                  </button>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b-2 border-gray-300">
                        <th className="text-left py-3 px-4 font-bold text-gray-700">Date</th>
                        <th className="text-left py-3 px-4 font-bold text-gray-700">Description</th>
                        <th className="text-left py-3 px-4 font-bold text-gray-700">Reference</th>
                        <th className="text-right py-3 px-4 font-bold text-gray-700">Debit</th>
                        <th className="text-right py-3 px-4 font-bold text-gray-700">Credit</th>
                        <th className="text-right py-3 px-4 font-bold text-gray-700">Balance</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedLedger.entries.map((entry, index) => (
                        <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-3 px-4 text-sm">
                            {new Date(entry.date).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4 text-sm">{entry.description}</td>
                          <td className="py-3 px-4 text-sm font-mono">{entry.reference || '—'}</td>
                          <td className="py-3 px-4 text-right font-mono">
                            {entry.debit > 0 ? (
                              <span className="text-red-600">${entry.debit.toLocaleString()}</span>
                            ) : (
                              <span className="text-gray-400">—</span>
                            )}
                          </td>
                          <td className="py-3 px-4 text-right font-mono">
                            {entry.credit > 0 ? (
                              <span className="text-green-600">${entry.credit.toLocaleString()}</span>
                            ) : (
                              <span className="text-gray-400">—</span>
                            )}
                          </td>
                          <td className="py-3 px-4 text-right font-mono font-medium">
                            <span className={entry.runningBalance >= 0 ? 'text-green-600' : 'text-red-600'}>
                              ${Math.abs(entry.runningBalance).toLocaleString()}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Information Note */}
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Real-time General Ledger
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    This general ledger shows all transactions for each account with running balances. 
                    It updates automatically when payments are recorded, invoices are created, or journal entries are added.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default GeneralLedger;
