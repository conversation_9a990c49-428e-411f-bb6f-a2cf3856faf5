import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function Dashboard() {
  const data = useQuery(api.finance.getDashboardData);

  if (!data) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Finance Dashboard</h1>
            <p className="text-gray-500">Loading data...</p>
          </main>
        </div>
      </div>
    );
  }

  const { revenue, expenses } = data;
  const profit = revenue - expenses;

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <h1 className="text-3xl font-bold mb-6">Finance Dashboard</h1>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card title="Revenue" value={revenue} color="text-green-600" />
            <Card title="Expenses" value={expenses} color="text-red-600" />
            <Card
              title="Profit"
              value={profit}
              color={profit >= 0 ? "text-green-700" : "text-red-700"}
            />
          </div>
        </main>
      </div>
    </div>
  );
}

function Card({ title, value, color }: { title: string; value: number; color: string }) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className={`text-2xl font-bold ${color}`}>
        ${Number(value).toLocaleString()}
      </p>
    </div>
  );
}

export default Dashboard;
