import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function Dashboard() {
  const data = useQuery(api.tasks.getDashboardData);
  const seedData = useMutation(api.tasks.seedAllData);

  const handleSeedData = async () => {
    try {
      await seedData();
      alert("Sample data has been created successfully!");
    } catch (error) {
      alert("Error creating sample data: " + error);
    }
  };

  if (!data) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Finance Dashboard</h1>
            <p className="text-gray-500">Loading data...</p>
          </main>
        </div>
      </div>
    );
  }

  const {
    revenue,
    expenses,
    profit,
    totalAssets,
    totalLiabilities,
    netWorth,
    totalInvoices,
    paidInvoices,
    pendingInvoices,
    overdueInvoices,
    totalInvoiceAmount,
    paidInvoiceAmount,
    outstandingInvoiceAmount,
    totalPayments,
    totalCustomers,
    recentTransactions,
    accountsSummary
  } = data;

  // Check if database is empty
  const isDatabaseEmpty = revenue === 0 && expenses === 0 && totalInvoices === 0;

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Finance Dashboard</h1>
            {isDatabaseEmpty && (
              <button
                type="button"
                onClick={handleSeedData}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
              >
                Load Sample Data
              </button>
            )}
          </div>

          {isDatabaseEmpty ? (
            <div className="bg-white p-8 rounded-lg shadow-md text-center">
              <h2 className="text-xl font-semibold mb-4">No Data Available</h2>
              <p className="text-gray-600 mb-4">
                Your database appears to be empty. Click the "Load Sample Data" button above to populate it with sample financial data.
              </p>
            </div>
          ) : (
            <>
              {/* Financial Overview Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <Card title="Revenue" value={revenue} color="text-green-600" />
                <Card title="Expenses" value={expenses} color="text-red-600" />
                <Card
                  title="Profit"
                  value={profit}
                  color={profit >= 0 ? "text-green-700" : "text-red-700"}
                />
                <Card
                  title="Net Worth"
                  value={netWorth}
                  color={netWorth >= 0 ? "text-blue-600" : "text-red-600"}
                />
              </div>

              {/* Assets & Liabilities */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <Card title="Total Assets" value={totalAssets} color="text-blue-600" />
                <Card title="Total Liabilities" value={totalLiabilities} color="text-orange-600" />
              </div>

              {/* Invoice Overview */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <StatCard title="Total Invoices" value={totalInvoices} />
                <StatCard title="Paid Invoices" value={paidInvoices} color="text-green-600" />
                <StatCard title="Pending Invoices" value={pendingInvoices} color="text-yellow-600" />
                <StatCard title="Overdue Invoices" value={overdueInvoices} color="text-red-600" />
              </div>

              {/* Invoice Amounts */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <Card title="Total Invoice Amount" value={totalInvoiceAmount} color="text-blue-600" />
                <Card title="Paid Amount" value={paidInvoiceAmount} color="text-green-600" />
                <Card title="Outstanding Amount" value={outstandingInvoiceAmount} color="text-orange-600" />
              </div>

              {/* Additional Metrics */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <StatCard title="Total Customers" value={totalCustomers} />
                <Card title="Total Payments" value={totalPayments} color="text-green-600" />
                <StatCard title="Total Accounts" value={accountsSummary.totalAccounts} />
              </div>

              {/* Recent Transactions */}
              {recentTransactions.length > 0 && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-semibold mb-4">Recent Transactions</h2>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm text-left">
                      <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3">Date</th>
                          <th scope="col" className="px-6 py-3">Account</th>
                          <th scope="col" className="px-6 py-3">Description</th>
                          <th scope="col" className="px-6 py-3">Debit</th>
                          <th scope="col" className="px-6 py-3">Credit</th>
                          <th scope="col" className="px-6 py-3">Reference</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentTransactions.map((transaction, index) => (
                          <tr key={index} className="bg-white border-b hover:bg-gray-50">
                            <td className="px-6 py-4 font-medium text-gray-900">
                              {new Date(transaction.date).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4">
                              <div>
                                <div className="font-medium">{transaction.accountName}</div>
                                <div className="text-gray-500 text-xs">{transaction.accountCode}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4">{transaction.description}</td>
                            <td className="px-6 py-4 text-red-600">
                              {transaction.debit > 0 ? `$${transaction.debit.toLocaleString()}` : '-'}
                            </td>
                            <td className="px-6 py-4 text-green-600">
                              {transaction.credit > 0 ? `$${transaction.credit.toLocaleString()}` : '-'}
                            </td>
                            <td className="px-6 py-4 text-gray-500">{transaction.reference || '-'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </>
          )}
        </main>
      </div>
    </div>
  );
}

function Card({ title, value, color }: { title: string; value: number; color: string }) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className={`text-2xl font-bold ${color}`}>
        ${Number(value).toLocaleString()}
      </p>
    </div>
  );
}

function StatCard({ title, value, color = "text-gray-800" }: { title: string; value: number; color?: string }) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className={`text-2xl font-bold ${color}`}>
        {Number(value).toLocaleString()}
      </p>
    </div>
  );
}

export default Dashboard;
