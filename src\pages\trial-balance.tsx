import React from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function TrialBalance() {
  const trialBalanceData = useQuery(api.tasks.getTrialBalance);

  if (!trialBalanceData) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Trial Balance</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  const { accounts, summary } = trialBalanceData;

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Trial Balance</h1>
            <div className="text-sm text-gray-600">
              As of {new Date().toLocaleDateString()}
            </div>
          </div>

          {/* Balance Status */}
          <div className="mb-6">
            <div className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium ${
              summary.isBalanced 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {summary.isBalanced ? '✅ Books are balanced' : '⚠️ Books are out of balance'}
              {!summary.isBalanced && (
                <span className="ml-2">
                  (Difference: ${Math.abs(summary.difference).toLocaleString()})
                </span>
              )}
            </div>
          </div>

          {/* Trial Balance Table */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b-2 border-gray-300">
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Account Code</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Account Name</th>
                    <th className="text-center py-3 px-4 font-bold text-gray-700">Type</th>
                    <th className="text-right py-3 px-4 font-bold text-gray-700">Debit Balance</th>
                    <th className="text-right py-3 px-4 font-bold text-gray-700">Credit Balance</th>
                  </tr>
                </thead>
                <tbody>
                  {accounts.map((account, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 font-mono text-sm">{account.accountCode}</td>
                      <td className="py-3 px-4 font-medium">{account.accountName}</td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          account.accountType === 'Asset' ? 'bg-blue-100 text-blue-800' :
                          account.accountType === 'Liability' ? 'bg-red-100 text-red-800' :
                          account.accountType === 'Equity' ? 'bg-purple-100 text-purple-800' :
                          account.accountType === 'Revenue' ? 'bg-green-100 text-green-800' :
                          'bg-orange-100 text-orange-800'
                        }`}>
                          {account.accountType}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-right font-mono">
                        {account.balance > 0 && (account.accountType === 'Asset' || account.accountType === 'Expense') ? (
                          <span className="text-gray-900">${account.balance.toLocaleString()}</span>
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                      <td className="py-3 px-4 text-right font-mono">
                        {account.balance > 0 && (account.accountType === 'Liability' || account.accountType === 'Equity' || account.accountType === 'Revenue') ? (
                          <span className="text-gray-900">${account.balance.toLocaleString()}</span>
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                    </tr>
                  ))}
                  
                  {/* Totals Row */}
                  <tr className="border-t-2 border-gray-300 bg-gray-50">
                    <td className="py-4 px-4 font-bold" colSpan={3}>TOTALS</td>
                    <td className="py-4 px-4 text-right font-bold text-lg">
                      ${summary.totalDebits.toLocaleString()}
                    </td>
                    <td className="py-4 px-4 text-right font-bold text-lg">
                      ${summary.totalCredits.toLocaleString()}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-sm font-medium text-gray-500">Total Accounts</div>
              <div className="text-2xl font-bold text-gray-900">{summary.accountCount}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-sm font-medium text-gray-500">Total Debits</div>
              <div className="text-2xl font-bold text-blue-600">${summary.totalDebits.toLocaleString()}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-sm font-medium text-gray-500">Total Credits</div>
              <div className="text-2xl font-bold text-green-600">${summary.totalCredits.toLocaleString()}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-sm font-medium text-gray-500">Difference</div>
              <div className={`text-2xl font-bold ${
                summary.isBalanced ? 'text-green-600' : 'text-red-600'
              }`}>
                ${Math.abs(summary.difference).toLocaleString()}
              </div>
            </div>
          </div>

          {/* Information Note */}
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Real-time Trial Balance
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    This trial balance updates automatically when payments are recorded, invoices are created, 
                    or journal entries are added. All balances reflect the current state of your accounting records.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default TrialBalance;
