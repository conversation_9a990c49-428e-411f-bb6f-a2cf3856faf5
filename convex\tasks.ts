// convex/tasks.ts
import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const getDashboardData = query(async (ctx) => {
  // Fetch all accounts to identify Revenue and Expense account IDs
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();
  const invoices = await ctx.db.query("invoices").collect();
  const customers = await ctx.db.query("customers").collect();
  const payments = await ctx.db.query("payments").collect();

  const revenueAccounts = accounts.filter((acc) => acc.type === "Revenue");
  const expenseAccounts = accounts.filter((acc) => acc.type === "Expense");
  const assetAccounts = accounts.filter((acc) => acc.type === "Asset");
  const liabilityAccounts = accounts.filter((acc) => acc.type === "Liability");

  const revenueAccountIds = revenueAccounts.map((acc) => acc._id);
  const expenseAccountIds = expenseAccounts.map((acc) => acc._id);
  const assetAccountIds = assetAccounts.map((acc) => acc._id);
  const liabilityAccountIds = liabilityAccounts.map((acc) => acc._id);

  // Calculate revenue and expenses
  const revenueEntries = journalEntries.filter((entry) =>
    revenueAccountIds.includes(entry.accountId)
  );

  const expenseEntries = journalEntries.filter((entry) =>
    expenseAccountIds.includes(entry.accountId)
  );

  const assetEntries = journalEntries.filter((entry) =>
    assetAccountIds.includes(entry.accountId)
  );

  const liabilityEntries = journalEntries.filter((entry) =>
    liabilityAccountIds.includes(entry.accountId)
  );

  // Sum credit - debit for revenue (credit increases revenue)
  const revenue = revenueEntries.reduce((total, entry) => {
    return total + (entry.credit - entry.debit);
  }, 0);

  // Sum debit - credit for expenses (debit increases expenses)
  const expenses = expenseEntries.reduce((total, entry) => {
    return total + (entry.debit - entry.credit);
  }, 0);

  // Sum debit - credit for assets (debit increases assets)
  const totalAssets = assetEntries.reduce((total, entry) => {
    return total + (entry.debit - entry.credit);
  }, 0);

  // Sum credit - debit for liabilities (credit increases liabilities)
  const totalLiabilities = liabilityEntries.reduce((total, entry) => {
    return total + (entry.credit - entry.debit);
  }, 0);

  // Calculate invoice metrics
  const totalInvoices = invoices.length;
  const paidInvoices = invoices.filter(inv => inv.status === "Paid").length;
  const pendingInvoices = invoices.filter(inv => inv.status === "Pending").length;
  const overdueInvoices = invoices.filter(inv => inv.status === "Overdue").length;

  const totalInvoiceAmount = invoices.reduce((sum, inv) => sum + inv.totalAmount, 0);
  const paidInvoiceAmount = invoices
    .filter(inv => inv.status === "Paid")
    .reduce((sum, inv) => sum + inv.totalAmount, 0);
  const outstandingInvoiceAmount = totalInvoiceAmount - paidInvoiceAmount;

  // Get recent transactions (last 5)
  const recentTransactions = journalEntries
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5)
    .map(entry => {
      const account = accounts.find(acc => acc._id === entry.accountId);
      return {
        ...entry,
        accountName: account?.name || "Unknown Account",
        accountCode: account?.code || "N/A"
      };
    });

  // Calculate total payments received
  const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);

  return {
    revenue,
    expenses,
    profit: revenue - expenses,
    totalAssets,
    totalLiabilities,
    netWorth: totalAssets - totalLiabilities,
    totalInvoices,
    paidInvoices,
    pendingInvoices,
    overdueInvoices,
    totalInvoiceAmount,
    paidInvoiceAmount,
    outstandingInvoiceAmount,
    totalPayments,
    totalCustomers: customers.length,
    recentTransactions,
    accountsSummary: {
      totalAccounts: accounts.length,
      revenueAccounts: revenueAccounts.length,
      expenseAccounts: expenseAccounts.length,
      assetAccounts: assetAccounts.length,
      liabilityAccounts: liabilityAccounts.length,
    }
  };
});

export const seedAllData = mutation(async (ctx) => {
  try {
    // Check if any data already exists
    const existingAccounts = await ctx.db.query("accounts").collect();
    const existingCustomers = await ctx.db.query("customers").collect();
    const existingJournalEntries = await ctx.db.query("journalEntries").collect();
    const existingInvoices = await ctx.db.query("invoices").collect();
    const existingPayments = await ctx.db.query("payments").collect();

    if (existingAccounts.length > 0 || existingCustomers.length > 0 ||
        existingJournalEntries.length > 0 || existingInvoices.length > 0 ||
        existingPayments.length > 0) {
      return {
        message: "Data already exists in the database",
        counts: {
          accounts: existingAccounts.length,
          customers: existingCustomers.length,
          journalEntries: existingJournalEntries.length,
          invoices: existingInvoices.length,
          payments: existingPayments.length
        }
      };
    }

    // Enhanced Chart of Accounts based on NEW JOY LEAN Enterprise structure
    const accounts = [
      // ASSETS
      // Current Assets (100s)
      { name: "Cash", type: "Asset" as const, code: "111", subType: "Current Asset" as const },
      { name: "Petty Cash Fund", type: "Asset" as const, code: "112", subType: "Current Asset" as const },
      { name: "Accounts Receivable", type: "Asset" as const, code: "113", subType: "Current Asset" as const },
      { name: "Allowance for Bad Debts", type: "Asset" as const, code: "114", subType: "Current Asset" as const },
      { name: "Merchandise Inventory", type: "Asset" as const, code: "115", subType: "Inventory" as const },
      { name: "Office Supplies", type: "Asset" as const, code: "116", subType: "Current Asset" as const },
      { name: "Prepaid Rent", type: "Asset" as const, code: "117", subType: "Current Asset" as const },
      { name: "Input Tax", type: "Asset" as const, code: "118", subType: "Current Asset" as const },
      { name: "Furniture & Fixtures", type: "Asset" as const, code: "119", subType: "Fixed Asset" as const },
      { name: "Accum Deprn - Furn & Fixtures", type: "Asset" as const, code: "121", subType: "Fixed Asset" as const },
      { name: "Store Equipment", type: "Asset" as const, code: "122", subType: "Fixed Asset" as const },
      { name: "Accum Deprn - Store Equipt", type: "Asset" as const, code: "123", subType: "Fixed Asset" as const },

      // LIABILITIES (200s)
      { name: "Accounts Payable", type: "Liability" as const, code: "200", subType: "Current Liability" as const },
      { name: "Output Tax", type: "Liability" as const, code: "201", subType: "Current Liability" as const },
      { name: "SSS Premium Payable", type: "Liability" as const, code: "203", subType: "Current Liability" as const },
      { name: "Philhealth Prem Payable", type: "Liability" as const, code: "204", subType: "Current Liability" as const },
      { name: "Pag-ibig Prem Payable", type: "Liability" as const, code: "205", subType: "Current Liability" as const },
      { name: "Withholding Tax Payable", type: "Liability" as const, code: "206", subType: "Current Liability" as const },

      // CAPITAL (300s)
      { name: "Jan, Capital", type: "Equity" as const, code: "300" },
      { name: "Jan, Withdrawals", type: "Equity" as const, code: "301" },
      { name: "Income & Expense Summary", type: "Equity" as const, code: "302" },

      // INCOME (400s)
      { name: "Sales", type: "Revenue" as const, code: "400", subType: "Operating Revenue" as const },
      { name: "Sales Discounts", type: "Revenue" as const, code: "401", subType: "Operating Revenue" as const },
      { name: "Sales Returns & Allowances", type: "Revenue" as const, code: "402", subType: "Operating Revenue" as const },

      // COST (500s)
      { name: "Purchases", type: "Expense" as const, code: "500", subType: "Cost of Goods Sold" as const },
      { name: "Purchase Discounts", type: "Expense" as const, code: "501", subType: "Cost of Goods Sold" as const },
      { name: "Purchase Returns & Allowances", type: "Expense" as const, code: "502", subType: "Cost of Goods Sold" as const },
      { name: "Freight In Transportation In", type: "Expense" as const, code: "503", subType: "Cost of Goods Sold" as const },

      // EXPENSES (500s continued)
      { name: "Bad Debts", type: "Expense" as const, code: "504", subType: "Operating Expense" as const },
      { name: "Depreciation Expense", type: "Expense" as const, code: "506", subType: "Operating Expense" as const },
      { name: "Gas & Water", type: "Expense" as const, code: "507", subType: "Operating Expense" as const },
      { name: "Taxes & Licenses", type: "Expense" as const, code: "508", subType: "Operating Expense" as const },
      { name: "Freight Out/Transportation Out", type: "Expense" as const, code: "509", subType: "Operating Expense" as const },
      { name: "Utilities Expense", type: "Expense" as const, code: "510", subType: "Operating Expense" as const },
      { name: "Rent Expense", type: "Expense" as const, code: "511", subType: "Operating Expense" as const },
      { name: "Salaries Expense", type: "Expense" as const, code: "512", subType: "Operating Expense" as const },
      { name: "SSS Contribution", type: "Expense" as const, code: "513", subType: "Operating Expense" as const },
      { name: "Philhealth Contribution", type: "Expense" as const, code: "514", subType: "Operating Expense" as const },
      { name: "Pag-ibig Contribution", type: "Expense" as const, code: "515", subType: "Operating Expense" as const },
      { name: "Miscellaneous Expense", type: "Expense" as const, code: "520", subType: "Operating Expense" as const },
    ];

    const createdAccounts: Array<{ id: any; name: string; type: string; code: string }> = [];
    for (const account of accounts) {
      const id = await ctx.db.insert("accounts", account);
      createdAccounts.push({ id, ...account });
    }

    // Seed customers
    const customers = [
      {
        name: "ABC Manufacturing Corp",
        contactEmail: "<EMAIL>",
        phone: "******-0101",
        address: "123 Industrial Blvd, Manufacturing City, MC 12345"
      },
      {
        name: "XYZ Retail Solutions",
        contactEmail: "<EMAIL>",
        phone: "******-0102",
        address: "456 Commerce St, Retail Town, RT 67890"
      },
      {
        name: "Global Tech Industries",
        contactEmail: "<EMAIL>",
        phone: "******-0103",
        address: "789 Technology Ave, Tech City, TC 11111"
      },
      {
        name: "Local Services LLC",
        contactEmail: "<EMAIL>",
        phone: "******-0104",
        address: "321 Service Road, Service Village, SV 22222"
      },
      {
        name: "Premium Products Inc",
        contactEmail: "<EMAIL>",
        phone: "******-0105",
        address: "654 Premium Plaza, Luxury Lane, LL 33333"
      }
    ];

    const createdCustomers: Array<{ id: any; name: string; contactEmail: string; phone: string; address: string }> = [];
    for (const customer of customers) {
      const id = await ctx.db.insert("customers", customer);
      createdCustomers.push({ id, ...customer });
    }

    // Add sample journal entries based on NEW JOY LEAN Enterprise transactions
    const sampleJournalEntries = [
      // Opening balances from the trial balance
      {
        accountId: createdAccounts.find(acc => acc.code === "111")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Cash in Bank",
        debit: 25000,
        credit: 0,
        reference: "OB-001",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "112")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Petty Cash Fund",
        debit: 1000,
        credit: 0,
        reference: "OB-002",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "113")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Accounts Receivable",
        debit: 50000,
        credit: 0,
        reference: "OB-003",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "115")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Merchandise Inventory",
        debit: 180000,
        credit: 0,
        reference: "OB-004",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "116")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Office Supplies",
        debit: 10000,
        credit: 0,
        reference: "OB-005",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "117")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Prepaid Rent",
        debit: 20000,
        credit: 0,
        reference: "OB-006",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "119")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Furniture & Fixtures",
        debit: 230000,
        credit: 0,
        reference: "OB-007",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "122")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Store Equipment",
        debit: 150000,
        credit: 0,
        reference: "OB-008",
        transactionType: "Adjustment" as const
      },
      // Liabilities
      {
        accountId: createdAccounts.find(acc => acc.code === "200")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Accounts Payable",
        debit: 0,
        credit: 11000,
        reference: "OB-009",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "203")?.id,
        date: "2024-01-01",
        description: "Opening Balance - SSS Premium Payable",
        debit: 0,
        credit: 2000,
        reference: "OB-010",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "204")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Philhealth Prem Payable",
        debit: 0,
        credit: 1500,
        reference: "OB-011",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "205")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Pag-ibig Prem Payable",
        debit: 0,
        credit: 1000,
        reference: "OB-012",
        transactionType: "Adjustment" as const
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "206")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Withholding Tax Payable",
        debit: 0,
        credit: 5000,
        reference: "OB-013",
        transactionType: "Adjustment" as const
      },
      // Capital
      {
        accountId: createdAccounts.find(acc => acc.code === "300")?.id,
        date: "2024-01-01",
        description: "Opening Balance - Jan, Capital",
        debit: 0,
        credit: 647500,
        reference: "OB-014",
        transactionType: "Adjustment" as const
      }
    ];

    const createdJournalEntries: Array<any> = [];
    for (const entry of sampleJournalEntries) {
      if (entry.accountId) {
        const id = await ctx.db.insert("journalEntries", entry);
        createdJournalEntries.push({ id, ...entry });
      }
    }

    return {
      message: "NEW JOY LEAN Enterprise financial data created successfully",
      counts: {
        accounts: createdAccounts.length,
        customers: createdCustomers.length,
        journalEntries: createdJournalEntries.length,
        invoices: 0,
        payments: 0
      }
    };
  } catch (error) {
    throw new Error(`Failed to seed data: ${error}`);
  }
});

// Function to add missing cost and expense accounts
export const addMissingCostExpenseAccounts = mutation(async (ctx) => {
  try {
    const existingAccounts = await ctx.db.query("accounts").collect();
    const existingCodes = new Set(existingAccounts.map(acc => acc.code));

    // Define the missing cost and expense accounts
    const missingAccounts = [
      // Cost of Goods Sold
      { name: "Purchases", type: "Expense" as const, code: "500", subType: "Cost of Goods Sold" as const },
      { name: "Purchase Discounts", type: "Expense" as const, code: "501", subType: "Cost of Goods Sold" as const },
      { name: "Purchase Returns & Allowances", type: "Expense" as const, code: "502", subType: "Cost of Goods Sold" as const },
      { name: "Freight In Transportation In", type: "Expense" as const, code: "503", subType: "Cost of Goods Sold" as const },

      // Operating Expenses
      { name: "Bad Debts", type: "Expense" as const, code: "504", subType: "Operating Expense" as const },
      { name: "Depreciation Expense", type: "Expense" as const, code: "506", subType: "Operating Expense" as const },
      { name: "Gas & Water", type: "Expense" as const, code: "507", subType: "Operating Expense" as const },
      { name: "Taxes & Licenses", type: "Expense" as const, code: "508", subType: "Operating Expense" as const },
      { name: "Freight Out/Transportation Out", type: "Expense" as const, code: "509", subType: "Operating Expense" as const },
      { name: "Utilities Expense", type: "Expense" as const, code: "510", subType: "Operating Expense" as const },
      { name: "Rent Expense", type: "Expense" as const, code: "511", subType: "Operating Expense" as const },
      { name: "Salaries Expense", type: "Expense" as const, code: "512", subType: "Operating Expense" as const },
      { name: "SSS Contribution", type: "Expense" as const, code: "513", subType: "Operating Expense" as const },
      { name: "Philhealth Contribution", type: "Expense" as const, code: "514", subType: "Operating Expense" as const },
      { name: "Pag-ibig Contribution", type: "Expense" as const, code: "515", subType: "Operating Expense" as const },
      { name: "Miscellaneous Expense", type: "Expense" as const, code: "520", subType: "Operating Expense" as const }
    ];

    // Filter out accounts that already exist
    const accountsToAdd = missingAccounts.filter(account => !existingCodes.has(account.code));

    const createdAccounts: Array<any> = [];
    for (const account of accountsToAdd) {
      const id = await ctx.db.insert("accounts", account);
      createdAccounts.push({ id, ...account });
    }

    return {
      message: `Added ${createdAccounts.length} missing cost and expense accounts`,
      accountsAdded: createdAccounts.map(acc => `${acc.code} - ${acc.name}`),
      skipped: missingAccounts.length - createdAccounts.length
    };
  } catch (error) {
    throw new Error(`Failed to add missing accounts: ${error}`);
  }
});

// Function to add sample transactions from the provided financial data
export const addSampleTransactions = mutation(async (ctx) => {
  try {
    const accounts = await ctx.db.query("accounts").collect();

    // Sample transactions based on the provided financial statement
    const transactions = [
      // Purchase transaction
      {
        entries: [
          {
            accountCode: "500", // Purchases
            description: "Purchased merchandise from Sita Company",
            debit: 120000,
            credit: 0,
            reference: "PO-001"
          },
          {
            accountCode: "118", // Input Tax
            description: "VAT on purchases from Sita Company",
            debit: 14400,
            credit: 0,
            reference: "PO-001"
          },
          {
            accountCode: "200", // Accounts Payable
            description: "Purchased merchandise from Sita Company",
            debit: 0,
            credit: 134400,
            reference: "PO-001"
          }
        ]
      },
      // Sales transaction
      {
        entries: [
          {
            accountCode: "113", // Accounts Receivable
            description: "Sold merchandise to Joy Enterprise",
            debit: 90000,
            credit: 0,
            reference: "INV-001"
          },
          {
            accountCode: "400", // Sales
            description: "Sold merchandise to Joy Enterprise",
            debit: 0,
            credit: 80000,
            reference: "INV-001"
          },
          {
            accountCode: "201", // Output Tax
            description: "VAT on sales to Joy Enterprise",
            debit: 0,
            credit: 9600,
            reference: "INV-001"
          }
        ]
      },
      // Freight expense
      {
        entries: [
          {
            accountCode: "509", // Freight Out/Transportation Out
            description: "Freight on shipment to customers",
            debit: 2450,
            credit: 0,
            reference: "FREIGHT-001"
          },
          {
            accountCode: "111", // Cash
            description: "Paid freight charges",
            debit: 0,
            credit: 2450,
            reference: "FREIGHT-001"
          }
        ]
      },
      // Freight from suppliers
      {
        entries: [
          {
            accountCode: "503", // Freight In Transportation In
            description: "Freight on shipment from suppliers",
            debit: 1000,
            credit: 0,
            reference: "FREIGHT-002"
          },
          {
            accountCode: "111", // Cash
            description: "Paid freight charges from suppliers",
            debit: 0,
            credit: 1000,
            reference: "FREIGHT-002"
          }
        ]
      }
    ];

    const createdEntries: Array<any> = [];

    for (const transaction of transactions) {
      for (const entry of transaction.entries) {
        const account = accounts.find(acc => acc.code === entry.accountCode);
        if (account) {
          const journalEntry = {
            accountId: account._id,
            date: "2024-01-15",
            description: entry.description,
            debit: entry.debit,
            credit: entry.credit,
            reference: entry.reference,
            transactionType: "Sales" as const
          };

          const id = await ctx.db.insert("journalEntries", journalEntry);
          createdEntries.push({ id, ...journalEntry });
        }
      }
    }

    return {
      message: "Sample transactions added successfully",
      transactionsAdded: createdEntries.length
    };
  } catch (error) {
    throw new Error(`Failed to add sample transactions: ${error}`);
  }
});

// Get Accounts Receivable Schedule
export const getAccountsReceivableSchedule = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  // Find Accounts Receivable account
  const arAccount = accounts.find(acc => acc.code === "113");
  if (!arAccount) return { schedule: [], total: 0 };

  // Get all AR entries
  const arEntries = journalEntries.filter(entry => entry.accountId === arAccount._id);

  // Sample breakdown based on the provided data
  const schedule = [
    { company: "Apex Corporation", amount: 25000 },
    { company: "Cecil Company", amount: 10000 },
    { company: "Joy Enterprise", amount: 15000 }
  ];

  const total = schedule.reduce((sum, item) => sum + item.amount, 0);

  return { schedule, total };
});

// Get Accounts Payable Schedule
export const getAccountsPayableSchedule = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  // Find Accounts Payable account
  const apAccount = accounts.find(acc => acc.code === "200");
  if (!apAccount) return { schedule: [], total: 0 };

  // Sample breakdown based on the provided data
  const schedule = [
    { company: "Apex Corporation", amount: 36000 },
    { company: "Sita Company", amount: 35000 }
  ];

  const total = schedule.reduce((sum, item) => sum + item.amount, 0);

  return { schedule, total };
});

export const getIncomeStatement = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  const revenueAccounts = accounts.filter((acc) => acc.type === "Revenue");
  const expenseAccounts = accounts.filter((acc) => acc.type === "Expense");

  const revenueData = revenueAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.credit - entry.debit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const expenseData = expenseAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.debit - entry.credit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const totalRevenue = revenueData.reduce((sum, item) => sum + item.balance, 0);
  const totalExpenses = expenseData.reduce((sum, item) => sum + item.balance, 0);
  const netIncome = totalRevenue - totalExpenses;

  return {
    revenue: revenueData,
    expenses: expenseData,
    totalRevenue,
    totalExpenses,
    netIncome
  };
});

export const getBalanceSheet = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  const assetAccounts = accounts.filter((acc) => acc.type === "Asset");
  const liabilityAccounts = accounts.filter((acc) => acc.type === "Liability");
  const equityAccounts = accounts.filter((acc) => acc.type === "Equity");

  const assets = assetAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.debit - entry.credit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const liabilities = liabilityAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.credit - entry.debit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const equity = equityAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.credit - entry.debit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const totalAssets = assets.reduce((sum, item) => sum + item.balance, 0);
  const totalLiabilities = liabilities.reduce((sum, item) => sum + item.balance, 0);
  const totalEquity = equity.reduce((sum, item) => sum + item.balance, 0);

  return {
    assets,
    liabilities,
    equity,
    totalAssets,
    totalLiabilities,
    totalEquity
  };
});

export const getTrialBalance = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  const trialBalanceData = accounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const totalDebits = entries.reduce((sum, entry) => sum + entry.debit, 0);
    const totalCredits = entries.reduce((sum, entry) => sum + entry.credit, 0);

    // Calculate balance based on account type
    let balance = 0;
    if (account.type === "Asset" || account.type === "Expense") {
      balance = totalDebits - totalCredits; // Normal debit balance
    } else if (account.type === "Liability" || account.type === "Equity" || account.type === "Revenue") {
      balance = totalCredits - totalDebits; // Normal credit balance
    }

    return {
      accountCode: account.code,
      accountName: account.name,
      accountType: account.type,
      totalDebits,
      totalCredits,
      balance
    };
  }).filter(account => (account.totalDebits > 0 || account.totalCredits > 0 || account.balance !== 0));

  const totalDebits = trialBalanceData.reduce((sum, account) => sum + account.totalDebits, 0);
  const totalCredits = trialBalanceData.reduce((sum, account) => sum + account.totalCredits, 0);
  const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01; // Allow for small rounding differences

  return {
    accounts: trialBalanceData.sort((a, b) => a.accountCode.localeCompare(b.accountCode)),
    summary: {
      totalDebits,
      totalCredits,
      difference: totalDebits - totalCredits,
      isBalanced,
      accountCount: trialBalanceData.length
    }
  };
});

export const getAgingReport = query(async (ctx) => {
  const invoices = await ctx.db.query("invoices").collect();
  const customers = await ctx.db.query("customers").collect();

  const today = new Date();

  const agingData = invoices
    .filter(invoice => invoice.status !== "Paid")
    .map(invoice => {
      const customer = customers.find(c => c._id === invoice.customerId);
      const dueDate = new Date(invoice.dueDate);
      const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

      let ageCategory = "Current";
      if (daysPastDue > 0 && daysPastDue <= 30) ageCategory = "1-30 Days";
      else if (daysPastDue > 30 && daysPastDue <= 60) ageCategory = "31-60 Days";
      else if (daysPastDue > 60 && daysPastDue <= 90) ageCategory = "61-90 Days";
      else if (daysPastDue > 90) ageCategory = "Over 90 Days";

      return {
        customerName: customer?.name || "Unknown Customer",
        invoiceNumber: invoice.invoiceNumber,
        dateIssued: invoice.dateIssued,
        dueDate: invoice.dueDate,
        amount: invoice.totalAmount,
        daysPastDue,
        ageCategory,
        status: invoice.status
      };
    });

  const summary = {
    current: agingData.filter(item => item.ageCategory === "Current").reduce((sum, item) => sum + item.amount, 0),
    days1to30: agingData.filter(item => item.ageCategory === "1-30 Days").reduce((sum, item) => sum + item.amount, 0),
    days31to60: agingData.filter(item => item.ageCategory === "31-60 Days").reduce((sum, item) => sum + item.amount, 0),
    days61to90: agingData.filter(item => item.ageCategory === "61-90 Days").reduce((sum, item) => sum + item.amount, 0),
    over90Days: agingData.filter(item => item.ageCategory === "Over 90 Days").reduce((sum, item) => sum + item.amount, 0)
  };

  const totalOutstanding = Object.values(summary).reduce((sum, amount) => sum + amount, 0);

  return {
    invoices: agingData,
    summary,
    totalOutstanding
  };
});

// Enhanced Financial Functions for Manufacturing

export const getCostCenterReport = query(async (ctx) => {
  const costCenters = await ctx.db.query("costCenters").collect();
  const budgets = await ctx.db.query("budgets").collect();
  const employees = await ctx.db.query("employees").collect();

  const costCenterData = costCenters.map(center => {
    const centerBudgets = budgets.filter(budget => budget.costCenterId === center._id);
    const manager = employees.find(emp => emp._id === center.managerId);

    const totalBudgeted = centerBudgets.reduce((sum, budget) => sum + budget.budgetedAmount, 0);
    const totalActual = centerBudgets.reduce((sum, budget) => sum + budget.actualAmount, 0);
    const variance = totalBudgeted - totalActual;
    const variancePercentage = totalBudgeted > 0 ? (variance / totalBudgeted) * 100 : 0;

    return {
      ...center,
      managerName: manager ? `${manager.firstName} ${manager.lastName}` : "Unassigned",
      totalBudgeted,
      totalActual,
      variance,
      variancePercentage,
      budgetCount: centerBudgets.length
    };
  });

  return {
    costCenters: costCenterData,
    summary: {
      totalCostCenters: costCenters.length,
      activeCostCenters: costCenters.filter(cc => cc.isActive).length,
      totalBudgetAmount: costCenterData.reduce((sum, cc) => sum + cc.totalBudgeted, 0),
      totalActualAmount: costCenterData.reduce((sum, cc) => sum + cc.totalActual, 0)
    }
  };
});

export const getFixedAssetsReport = query(async (ctx) => {
  const fixedAssets = await ctx.db.query("fixedAssets").collect();

  const assetData = fixedAssets.map(asset => {
    const ageInYears = (new Date().getTime() - new Date(asset.purchaseDate).getTime()) / (1000 * 60 * 60 * 24 * 365);
    const depreciationRate = asset.usefulLife > 0 ? 1 / asset.usefulLife : 0;
    const annualDepreciation = (asset.purchaseCost - asset.salvageValue) * depreciationRate;

    return {
      ...asset,
      ageInYears: Math.round(ageInYears * 10) / 10,
      annualDepreciation,
      depreciationRate: depreciationRate * 100,
      remainingLife: Math.max(0, asset.usefulLife - ageInYears)
    };
  });

  const categoryBreakdown = {
    machinery: assetData.filter(asset => asset.category === "Machinery"),
    equipment: assetData.filter(asset => asset.category === "Equipment"),
    building: assetData.filter(asset => asset.category === "Building"),
    vehicle: assetData.filter(asset => asset.category === "Vehicle"),
    itEquipment: assetData.filter(asset => asset.category === "IT Equipment")
  };

  return {
    assets: assetData,
    categoryBreakdown,
    summary: {
      totalAssets: fixedAssets.length,
      totalPurchaseCost: fixedAssets.reduce((sum, asset) => sum + asset.purchaseCost, 0),
      totalCurrentValue: fixedAssets.reduce((sum, asset) => sum + asset.currentBookValue, 0),
      totalDepreciation: fixedAssets.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0),
      activeAssets: fixedAssets.filter(asset => asset.status === "Active").length
    }
  };
});

export const getAccountsPayableReport = query(async (ctx) => {
  const vendorInvoices = await ctx.db.query("vendorInvoices").collect();
  const suppliers = await ctx.db.query("suppliers").collect();
  const purchaseOrders = await ctx.db.query("purchaseOrders").collect();

  const payableData = vendorInvoices.map(invoice => {
    const supplier = suppliers.find(s => s._id === invoice.supplierId);
    const purchaseOrder = purchaseOrders.find(po => po._id === invoice.purchaseOrderId);
    const dueDate = new Date(invoice.dueDate);
    const today = new Date();
    const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      ...invoice,
      supplierName: supplier?.name || "Unknown Supplier",
      purchaseOrderNumber: purchaseOrder?.orderNumber || "N/A",
      daysPastDue: Math.max(0, daysPastDue),
      outstandingAmount: invoice.totalAmount - invoice.paidAmount
    };
  });

  const statusBreakdown = {
    pending: payableData.filter(inv => inv.status === "Pending"),
    approved: payableData.filter(inv => inv.status === "Approved"),
    paid: payableData.filter(inv => inv.status === "Paid"),
    overdue: payableData.filter(inv => inv.status === "Overdue")
  };

  // Create aging report by supplier
  const agingReport = suppliers.map(supplier => {
    const supplierInvoices = payableData.filter(inv => inv.supplierId === supplier._id);

    const current = supplierInvoices.filter(inv => inv.daysPastDue === 0).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days1to30 = supplierInvoices.filter(inv => inv.daysPastDue > 0 && inv.daysPastDue <= 30).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days31to60 = supplierInvoices.filter(inv => inv.daysPastDue > 30 && inv.daysPastDue <= 60).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days61to90 = supplierInvoices.filter(inv => inv.daysPastDue > 60 && inv.daysPastDue <= 90).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days90Plus = supplierInvoices.filter(inv => inv.daysPastDue > 90).reduce((sum, inv) => sum + inv.outstandingAmount, 0);

    return {
      supplierId: supplier._id,
      supplierName: supplier.name,
      current,
      days1to30,
      days31to60,
      days61to90,
      days90Plus,
      total: current + days1to30 + days31to60 + days61to90 + days90Plus
    };
  }).filter(supplier => supplier.total > 0);

  return {
    payables: payableData,
    agingReport,
    statusBreakdown,
    summary: {
      totalBills: vendorInvoices.length,
      totalAmount: vendorInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0),
      totalPaid: vendorInvoices.reduce((sum, inv) => sum + inv.paidAmount, 0),
      totalOutstanding: vendorInvoices.reduce((sum, inv) => sum + (inv.totalAmount - inv.paidAmount), 0),
      overdueAmount: payableData.filter(inv => inv.daysPastDue > 0).reduce((sum, inv) => sum + inv.outstandingAmount, 0),
      overdueCount: statusBreakdown.overdue.length,
      averageDaysOutstanding: payableData.length > 0 ? payableData.reduce((sum, inv) => sum + inv.daysPastDue, 0) / payableData.length : 0
    }
  };
});

export const getCustomers = query(async (ctx) => {
  return await ctx.db.query("customers").collect();
});

export const getSuppliers = query(async (ctx) => {
  return await ctx.db.query("suppliers").collect();
});

// CRUD Operations for Accounts
export const getAccounts = query(async (ctx) => {
  return await ctx.db.query("accounts").order("asc").collect();
});

export const addAccount = mutation({
  args: {
    name: v.string(),
    code: v.string(),
    type: v.union(v.literal("Asset"), v.literal("Liability"), v.literal("Equity"), v.literal("Revenue"), v.literal("Expense")),
    subType: v.optional(v.union(
      v.literal("Current Asset"),
      v.literal("Fixed Asset"),
      v.literal("Inventory"),
      v.literal("Current Liability"),
      v.literal("Long-term Liability"),
      v.literal("Operating Revenue"),
      v.literal("Non-operating Revenue"),
      v.literal("Cost of Goods Sold"),
      v.literal("Operating Expense"),
      v.literal("Administrative Expense")
    ))
  },
  handler: async (ctx, args) => {
    const insertData = {
      name: args.name,
      code: args.code,
      type: args.type,
      ...(args.subType && { subType: args.subType })
    };
    return await ctx.db.insert("accounts", insertData);
  }
});

export const updateAccount = mutation({
  args: {
    id: v.id("accounts"),
    name: v.string(),
    code: v.string(),
    type: v.union(v.literal("Asset"), v.literal("Liability"), v.literal("Equity"), v.literal("Revenue"), v.literal("Expense")),
    subType: v.optional(v.union(
      v.literal("Current Asset"),
      v.literal("Fixed Asset"),
      v.literal("Inventory"),
      v.literal("Current Liability"),
      v.literal("Long-term Liability"),
      v.literal("Operating Revenue"),
      v.literal("Non-operating Revenue"),
      v.literal("Cost of Goods Sold"),
      v.literal("Operating Expense"),
      v.literal("Administrative Expense")
    ))
  },
  handler: async (ctx, args) => {
    const { id, ...updateData } = args;

    // Build the patch data object
    const patchData: any = {
      name: updateData.name,
      code: updateData.code,
      type: updateData.type
    };

    // Handle subType properly - only include if it has a value, otherwise set to undefined
    if (updateData.subType && updateData.subType.trim() !== "") {
      patchData.subType = updateData.subType;
    } else {
      patchData.subType = undefined;
    }

    return await ctx.db.patch(id, patchData);
  }
});

export const deleteAccount = mutation({
  args: {
    id: v.id("accounts")
  },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.id);
  }
});

// CRUD Operations for Customers
export const addCustomer = mutation({
  args: {
    name: v.string(),
    contactEmail: v.string(),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
    customerType: v.optional(v.union(v.literal("OEM"), v.literal("Aftermarket"), v.literal("Distributor"))),
    creditLimit: v.optional(v.number()),
    paymentTerms: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("customers", args);
  }
});

export const updateCustomer = mutation({
  args: {
    id: v.id("customers"),
    name: v.string(),
    contactEmail: v.string(),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
    customerType: v.optional(v.union(v.literal("OEM"), v.literal("Aftermarket"), v.literal("Distributor"))),
    creditLimit: v.optional(v.number()),
    paymentTerms: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const { id, ...updateData } = args;
    return await ctx.db.patch(id, updateData);
  }
});

export const deleteCustomer = mutation({
  args: {
    id: v.id("customers")
  },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.id);
  }
});

// CRUD Operations for Suppliers
export const addSupplier = mutation({
  args: {
    name: v.string(),
    contactEmail: v.string(),
    phone: v.string(),
    address: v.string(),
    supplierType: v.union(v.literal("Raw Material"), v.literal("Components"), v.literal("Equipment"), v.literal("Services")),
    paymentTerms: v.string(),
    qualityRating: v.number()
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("suppliers", args);
  }
});

export const updateSupplier = mutation({
  args: {
    id: v.id("suppliers"),
    name: v.string(),
    contactEmail: v.string(),
    phone: v.string(),
    address: v.string(),
    supplierType: v.union(v.literal("Raw Material"), v.literal("Components"), v.literal("Equipment"), v.literal("Services")),
    paymentTerms: v.string(),
    qualityRating: v.number()
  },
  handler: async (ctx, args) => {
    const { id, ...updateData } = args;
    return await ctx.db.patch(id, updateData);
  }
});

export const deleteSupplier = mutation({
  args: {
    id: v.id("suppliers")
  },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.id);
  }
});

// CRUD Operations for Journal Entries
export const getJournalEntries = query(async (ctx) => {
  return await ctx.db.query("journalEntries").order("desc").collect();
});

export const addJournalEntry = mutation({
  args: {
    accountId: v.id("accounts"),
    date: v.string(),
    description: v.string(),
    debit: v.number(),
    credit: v.number(),
    reference: v.optional(v.string()),
    transactionType: v.optional(v.union(
      v.literal("Sales"),
      v.literal("Purchase"),
      v.literal("Production"),
      v.literal("Payroll"),
      v.literal("Depreciation"),
      v.literal("Adjustment")
    )),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("journalEntries", args);
  }
});

export const deleteJournalEntry = mutation({
  args: {
    id: v.id("journalEntries")
  },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.id);
  }
});

// CRUD Operations for Customer Invoices
export const addCustomerInvoice = mutation({
  args: {
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    totalAmount: v.number(),
    taxAmount: v.number(),
    discountAmount: v.number(),
    salesOrderId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const invoiceData = {
      ...args,
      paidAmount: 0,
      status: "Draft" as const
    };

    // Create the invoice
    const invoiceId = await ctx.db.insert("customerInvoices", invoiceData);

    // Get customer information for journal entries
    const customer = await ctx.db.get(args.customerId);
    const customerName = customer?.name || "Unknown Customer";

    // Find required accounts for journal entries
    const accounts = await ctx.db.query("accounts").collect();
    const arAccount = accounts.find(acc => acc.name.toLowerCase().includes("accounts receivable") || acc.name.toLowerCase().includes("receivable"));
    const revenueAccount = accounts.find(acc => acc.type === "Revenue" && (acc.name.toLowerCase().includes("sales") || acc.name.toLowerCase().includes("revenue")));
    const taxAccount = accounts.find(acc => acc.name.toLowerCase().includes("tax") && acc.type === "Liability");

    if (!arAccount || !revenueAccount) {
      // If accounts don't exist, still create the invoice but warn the user
      console.warn("Required accounts (Accounts Receivable and Sales Revenue) not found. Journal entries not created.");
      return invoiceId;
    }

    // Calculate net amount (total - tax)
    const netAmount = args.totalAmount - args.taxAmount;

    // Create journal entries for the invoice
    // Debit: Accounts Receivable (increase asset)
    await ctx.db.insert("journalEntries", {
      accountId: arAccount._id,
      date: args.invoiceDate,
      description: `Invoice ${args.invoiceNumber} for ${customerName}`,
      debit: args.totalAmount,
      credit: 0,
      reference: args.invoiceNumber,
      transactionType: "Sales",
      departmentId: "",
      costCenterId: ""
    });

    // Credit: Sales Revenue (increase revenue)
    await ctx.db.insert("journalEntries", {
      accountId: revenueAccount._id,
      date: args.invoiceDate,
      description: `Invoice ${args.invoiceNumber} for ${customerName}`,
      debit: 0,
      credit: netAmount,
      reference: args.invoiceNumber,
      transactionType: "Sales",
      departmentId: "",
      costCenterId: ""
    });

    // Credit: Tax Payable (if tax amount > 0 and tax account exists)
    if (args.taxAmount > 0 && taxAccount) {
      await ctx.db.insert("journalEntries", {
        accountId: taxAccount._id,
        date: args.invoiceDate,
        description: `Tax on Invoice ${args.invoiceNumber} for ${customerName}`,
        debit: 0,
        credit: args.taxAmount,
        reference: args.invoiceNumber,
        transactionType: "Sales",
        departmentId: "",
        costCenterId: ""
      });
    }

    return invoiceId;
  }
});

export const updateCustomerInvoiceStatus = mutation({
  args: {
    id: v.id("customerInvoices"),
    status: v.union(v.literal("Draft"), v.literal("Sent"), v.literal("Paid"), v.literal("Overdue"), v.literal("Cancelled"))
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.id, { status: args.status });
  }
});

export const recordCustomerPayment = mutation({
  args: {
    invoiceId: v.id("customerInvoices"),
    amount: v.number(),
    paymentDate: v.string(),
    paymentMethod: v.string(),
    reference: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) throw new Error("Invoice not found");

    const newPaidAmount = invoice.paidAmount + args.amount;
    const newStatus = newPaidAmount >= invoice.totalAmount ? "Paid" : invoice.status;

    // Update invoice payment status
    await ctx.db.patch(args.invoiceId, {
      paidAmount: newPaidAmount,
      status: newStatus
    });

    // Get customer information for journal entries
    const customer = await ctx.db.get(invoice.customerId);
    const customerName = customer?.name || "Unknown Customer";

    // Find required accounts for journal entries
    const accounts = await ctx.db.query("accounts").collect();
    const cashAccount = accounts.find(acc => acc.name.toLowerCase().includes("cash") || acc.name.toLowerCase().includes("bank"));
    const arAccount = accounts.find(acc => acc.name.toLowerCase().includes("accounts receivable") || acc.name.toLowerCase().includes("receivable"));

    if (!cashAccount || !arAccount) {
      throw new Error("Required accounts (Cash/Bank and Accounts Receivable) not found. Please set up your chart of accounts first.");
    }

    // Create journal entries for the payment
    // Debit: Cash/Bank Account (increase asset)
    await ctx.db.insert("journalEntries", {
      accountId: cashAccount._id,
      date: args.paymentDate,
      description: `Payment received from ${customerName} for Invoice ${invoice.invoiceNumber}`,
      debit: args.amount,
      credit: 0,
      reference: args.reference || invoice.invoiceNumber,
      transactionType: "Sales",
      departmentId: "",
      costCenterId: ""
    });

    // Credit: Accounts Receivable (decrease asset)
    await ctx.db.insert("journalEntries", {
      accountId: arAccount._id,
      date: args.paymentDate,
      description: `Payment received from ${customerName} for Invoice ${invoice.invoiceNumber}`,
      debit: 0,
      credit: args.amount,
      reference: args.reference || invoice.invoiceNumber,
      transactionType: "Sales",
      departmentId: "",
      costCenterId: ""
    });

    // Return payment details for confirmation
    return {
      success: true,
      newPaidAmount,
      newStatus,
      paymentAmount: args.amount,
      paymentDate: args.paymentDate,
      paymentMethod: args.paymentMethod,
      reference: args.reference,
      journalEntriesCreated: 2,
      message: `Payment recorded and journal entries created. Cash account debited and Accounts Receivable credited for $${args.amount.toLocaleString()}`
    };
  }
});

// CRUD Operations for Vendor Invoices (Bills)
export const addVendorInvoice = mutation({
  args: {
    supplierId: v.id("suppliers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    totalAmount: v.number(),
    description: v.string(),
    purchaseOrderId: v.optional(v.id("purchaseOrders"))
  },
  handler: async (ctx, args) => {
    const billData = {
      ...args,
      paidAmount: 0,
      status: "Pending" as const
    };

    // Create the vendor invoice
    const invoiceId = await ctx.db.insert("vendorInvoices", billData);

    // Get supplier information for journal entries
    const supplier = await ctx.db.get(args.supplierId);
    const supplierName = supplier?.name || "Unknown Supplier";

    // Find required accounts for journal entries
    const accounts = await ctx.db.query("accounts").collect();
    const apAccount = accounts.find(acc => acc.name.toLowerCase().includes("accounts payable") || acc.name.toLowerCase().includes("payable"));
    const expenseAccount = accounts.find(acc => acc.type === "Expense" && (acc.name.toLowerCase().includes("purchase") || acc.name.toLowerCase().includes("expense")));

    if (!apAccount || !expenseAccount) {
      // If accounts don't exist, still create the bill but warn the user
      console.warn("Required accounts (Accounts Payable and Expense) not found. Journal entries not created.");
      return invoiceId;
    }

    // Create journal entries for the bill
    // Debit: Expense Account (increase expense)
    await ctx.db.insert("journalEntries", {
      accountId: expenseAccount._id,
      date: args.invoiceDate,
      description: `Bill ${args.invoiceNumber} from ${supplierName} - ${args.description}`,
      debit: args.totalAmount,
      credit: 0,
      reference: args.invoiceNumber,
      transactionType: "Purchase",
      departmentId: "",
      costCenterId: ""
    });

    // Credit: Accounts Payable (increase liability)
    await ctx.db.insert("journalEntries", {
      accountId: apAccount._id,
      date: args.invoiceDate,
      description: `Bill ${args.invoiceNumber} from ${supplierName} - ${args.description}`,
      debit: 0,
      credit: args.totalAmount,
      reference: args.invoiceNumber,
      transactionType: "Purchase",
      departmentId: "",
      costCenterId: ""
    });

    return invoiceId;
  }
});

export const updateVendorInvoiceStatus = mutation({
  args: {
    id: v.id("vendorInvoices"),
    status: v.union(v.literal("Pending"), v.literal("Approved"), v.literal("Paid"), v.literal("Overdue"))
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.id, { status: args.status });
  }
});

export const recordVendorPayment = mutation({
  args: {
    invoiceId: v.id("vendorInvoices"),
    amount: v.number(),
    paymentDate: v.string(),
    paymentMethod: v.string(),
    reference: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) throw new Error("Vendor invoice not found");

    const newPaidAmount = invoice.paidAmount + args.amount;
    const newStatus = newPaidAmount >= invoice.totalAmount ? "Paid" : invoice.status;

    // Update invoice payment status
    await ctx.db.patch(args.invoiceId, {
      paidAmount: newPaidAmount,
      status: newStatus
    });

    // Get supplier information for journal entries
    const supplier = await ctx.db.get(invoice.supplierId);
    const supplierName = supplier?.name || "Unknown Supplier";

    // Find required accounts for journal entries
    const accounts = await ctx.db.query("accounts").collect();
    const cashAccount = accounts.find(acc => acc.name.toLowerCase().includes("cash") || acc.name.toLowerCase().includes("bank"));
    const apAccount = accounts.find(acc => acc.name.toLowerCase().includes("accounts payable") || acc.name.toLowerCase().includes("payable"));

    if (!cashAccount || !apAccount) {
      throw new Error("Required accounts (Cash/Bank and Accounts Payable) not found. Please set up your chart of accounts first.");
    }

    // Create journal entries for the payment
    // Debit: Accounts Payable (decrease liability)
    await ctx.db.insert("journalEntries", {
      accountId: apAccount._id,
      date: args.paymentDate,
      description: `Payment made to ${supplierName} for Invoice ${invoice.invoiceNumber}`,
      debit: args.amount,
      credit: 0,
      reference: args.reference || invoice.invoiceNumber,
      transactionType: "Purchase",
      departmentId: "",
      costCenterId: ""
    });

    // Credit: Cash/Bank Account (decrease asset)
    await ctx.db.insert("journalEntries", {
      accountId: cashAccount._id,
      date: args.paymentDate,
      description: `Payment made to ${supplierName} for Invoice ${invoice.invoiceNumber}`,
      debit: 0,
      credit: args.amount,
      reference: args.reference || invoice.invoiceNumber,
      transactionType: "Purchase",
      departmentId: "",
      costCenterId: ""
    });

    return {
      success: true,
      newPaidAmount,
      newStatus,
      paymentAmount: args.amount,
      paymentDate: args.paymentDate,
      paymentMethod: args.paymentMethod,
      reference: args.reference,
      journalEntriesCreated: 2,
      message: `Payment recorded and journal entries created. Accounts Payable debited and Cash account credited for $${args.amount.toLocaleString()}`
    };
  }
});

// Enhanced Financial Reports that update in real-time with journal entries
export const getGeneralLedger = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  const ledgerData = accounts.map(account => {
    const entries = journalEntries
      .filter(entry => entry.accountId === account._id)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    let runningBalance = 0;
    const entriesWithBalance = entries.map(entry => {
      if (account.type === "Asset" || account.type === "Expense") {
        runningBalance += (entry.debit - entry.credit);
      } else {
        runningBalance += (entry.credit - entry.debit);
      }

      return {
        ...entry,
        runningBalance
      };
    });

    return {
      account,
      entries: entriesWithBalance,
      finalBalance: runningBalance,
      totalDebits: entries.reduce((sum, entry) => sum + entry.debit, 0),
      totalCredits: entries.reduce((sum, entry) => sum + entry.credit, 0)
    };
  }).filter(ledger => ledger.entries.length > 0);

  return ledgerData;
});

export const getAccountsReceivableReport = query(async (ctx) => {
  const customerInvoices = await ctx.db.query("customerInvoices").collect();
  const customers = await ctx.db.query("customers").collect();

  const receivableData = customerInvoices.map(invoice => {
    const customer = customers.find(c => c._id === invoice.customerId);
    const dueDate = new Date(invoice.dueDate);
    const today = new Date();
    const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      ...invoice,
      customerName: customer?.name || "Unknown Customer",
      customerType: customer?.customerType || "Unknown",
      daysPastDue: Math.max(0, daysPastDue),
      outstandingAmount: invoice.totalAmount - invoice.paidAmount
    };
  });

  const statusBreakdown = {
    draft: receivableData.filter(inv => inv.status === "Draft"),
    sent: receivableData.filter(inv => inv.status === "Sent"),
    paid: receivableData.filter(inv => inv.status === "Paid"),
    overdue: receivableData.filter(inv => inv.status === "Overdue"),
    cancelled: receivableData.filter(inv => inv.status === "Cancelled")
  };

  const customerTypeBreakdown = {
    oem: receivableData.filter(inv => inv.customerType === "OEM"),
    aftermarket: receivableData.filter(inv => inv.customerType === "Aftermarket"),
    distributor: receivableData.filter(inv => inv.customerType === "Distributor")
  };

  // Create aging report by customer
  const agingReport = customers.map(customer => {
    const customerInvoices = receivableData.filter(inv => inv.customerId === customer._id);

    const current = customerInvoices.filter(inv => inv.daysPastDue === 0).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days1to30 = customerInvoices.filter(inv => inv.daysPastDue > 0 && inv.daysPastDue <= 30).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days31to60 = customerInvoices.filter(inv => inv.daysPastDue > 30 && inv.daysPastDue <= 60).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days61to90 = customerInvoices.filter(inv => inv.daysPastDue > 60 && inv.daysPastDue <= 90).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days90Plus = customerInvoices.filter(inv => inv.daysPastDue > 90).reduce((sum, inv) => sum + inv.outstandingAmount, 0);

    return {
      customerId: customer._id,
      customerName: customer.name,
      current,
      days1to30,
      days31to60,
      days61to90,
      days90Plus,
      total: current + days1to30 + days31to60 + days61to90 + days90Plus
    };
  }).filter(customer => customer.total > 0);

  return {
    receivables: receivableData,
    agingReport,
    statusBreakdown,
    customerTypeBreakdown,
    summary: {
      totalInvoices: customerInvoices.length,
      totalAmount: customerInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0),
      totalPaid: customerInvoices.reduce((sum, inv) => sum + inv.paidAmount, 0),
      totalOutstanding: customerInvoices.reduce((sum, inv) => sum + (inv.totalAmount - inv.paidAmount), 0),
      overdueAmount: receivableData.filter(inv => inv.daysPastDue > 0).reduce((sum, inv) => sum + inv.outstandingAmount, 0),
      overdueCount: statusBreakdown.overdue.length,
      averageDaysOutstanding: receivableData.length > 0 ? receivableData.reduce((sum, inv) => sum + inv.daysPastDue, 0) / receivableData.length : 0
    }
  };
});

export const getManufacturingCostReport = query(async (ctx) => {
  const workOrderCosts = await ctx.db.query("workOrderCosts").collect();
  const workOrders = await ctx.db.query("workOrders").collect();
  const productionOrders = await ctx.db.query("productionOrders").collect();
  const inventory = await ctx.db.query("inventory").collect();
  const accounts = await ctx.db.query("accounts").collect();

  const costData = workOrderCosts.map(cost => {
    const workOrder = workOrders.find(wo => wo._id === cost.workOrderId);
    const productionOrder = productionOrders.find(po => po._id === workOrder?.productionOrderId);
    const product = inventory.find(inv => inv._id === productionOrder?.productId);
    const account = accounts.find(acc => acc._id === cost.accountId);

    return {
      ...cost,
      workOrderNumber: workOrder?.workOrderNumber || "N/A",
      productionOrderNumber: productionOrder?.orderNumber || "N/A",
      productName: product?.partName || "Unknown Product",
      accountName: account?.name || "Unknown Account"
    };
  });

  const costByType = {
    material: costData.filter(cost => cost.costType === "Material"),
    labor: costData.filter(cost => cost.costType === "Labor"),
    overhead: costData.filter(cost => cost.costType === "Overhead")
  };

  const totalCosts = {
    material: costByType.material.reduce((sum, cost) => sum + cost.amount, 0),
    labor: costByType.labor.reduce((sum, cost) => sum + cost.amount, 0),
    overhead: costByType.overhead.reduce((sum, cost) => sum + cost.amount, 0)
  };

  return {
    costs: costData,
    costByType,
    totalCosts,
    summary: {
      totalManufacturingCost: Object.values(totalCosts).reduce((sum, cost) => sum + cost, 0),
      materialPercentage: totalCosts.material / (Object.values(totalCosts).reduce((sum, cost) => sum + cost, 0) || 1) * 100,
      laborPercentage: totalCosts.labor / (Object.values(totalCosts).reduce((sum, cost) => sum + cost, 0) || 1) * 100,
      overheadPercentage: totalCosts.overhead / (Object.values(totalCosts).reduce((sum, cost) => sum + cost, 0) || 1) * 100
    }
  };
});