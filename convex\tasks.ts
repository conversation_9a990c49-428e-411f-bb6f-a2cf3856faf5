// convex/tasks.ts
import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const getDashboardData = query(async (ctx) => {
  // Fetch all accounts to identify Revenue and Expense account IDs
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();
  const invoices = await ctx.db.query("invoices").collect();
  const customers = await ctx.db.query("customers").collect();
  const payments = await ctx.db.query("payments").collect();

  const revenueAccounts = accounts.filter((acc) => acc.type === "Revenue");
  const expenseAccounts = accounts.filter((acc) => acc.type === "Expense");
  const assetAccounts = accounts.filter((acc) => acc.type === "Asset");
  const liabilityAccounts = accounts.filter((acc) => acc.type === "Liability");

  const revenueAccountIds = revenueAccounts.map((acc) => acc._id);
  const expenseAccountIds = expenseAccounts.map((acc) => acc._id);
  const assetAccountIds = assetAccounts.map((acc) => acc._id);
  const liabilityAccountIds = liabilityAccounts.map((acc) => acc._id);

  // Calculate revenue and expenses
  const revenueEntries = journalEntries.filter((entry) =>
    revenueAccountIds.includes(entry.accountId)
  );

  const expenseEntries = journalEntries.filter((entry) =>
    expenseAccountIds.includes(entry.accountId)
  );

  const assetEntries = journalEntries.filter((entry) =>
    assetAccountIds.includes(entry.accountId)
  );

  const liabilityEntries = journalEntries.filter((entry) =>
    liabilityAccountIds.includes(entry.accountId)
  );

  // Sum credit - debit for revenue (credit increases revenue)
  const revenue = revenueEntries.reduce((total, entry) => {
    return total + (entry.credit - entry.debit);
  }, 0);

  // Sum debit - credit for expenses (debit increases expenses)
  const expenses = expenseEntries.reduce((total, entry) => {
    return total + (entry.debit - entry.credit);
  }, 0);

  // Sum debit - credit for assets (debit increases assets)
  const totalAssets = assetEntries.reduce((total, entry) => {
    return total + (entry.debit - entry.credit);
  }, 0);

  // Sum credit - debit for liabilities (credit increases liabilities)
  const totalLiabilities = liabilityEntries.reduce((total, entry) => {
    return total + (entry.credit - entry.debit);
  }, 0);

  // Calculate invoice metrics
  const totalInvoices = invoices.length;
  const paidInvoices = invoices.filter(inv => inv.status === "Paid").length;
  const pendingInvoices = invoices.filter(inv => inv.status === "Pending").length;
  const overdueInvoices = invoices.filter(inv => inv.status === "Overdue").length;

  const totalInvoiceAmount = invoices.reduce((sum, inv) => sum + inv.totalAmount, 0);
  const paidInvoiceAmount = invoices
    .filter(inv => inv.status === "Paid")
    .reduce((sum, inv) => sum + inv.totalAmount, 0);
  const outstandingInvoiceAmount = totalInvoiceAmount - paidInvoiceAmount;

  // Get recent transactions (last 5)
  const recentTransactions = journalEntries
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5)
    .map(entry => {
      const account = accounts.find(acc => acc._id === entry.accountId);
      return {
        ...entry,
        accountName: account?.name || "Unknown Account",
        accountCode: account?.code || "N/A"
      };
    });

  // Calculate total payments received
  const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);

  return {
    revenue,
    expenses,
    profit: revenue - expenses,
    totalAssets,
    totalLiabilities,
    netWorth: totalAssets - totalLiabilities,
    totalInvoices,
    paidInvoices,
    pendingInvoices,
    overdueInvoices,
    totalInvoiceAmount,
    paidInvoiceAmount,
    outstandingInvoiceAmount,
    totalPayments,
    totalCustomers: customers.length,
    recentTransactions,
    accountsSummary: {
      totalAccounts: accounts.length,
      revenueAccounts: revenueAccounts.length,
      expenseAccounts: expenseAccounts.length,
      assetAccounts: assetAccounts.length,
      liabilityAccounts: liabilityAccounts.length,
    }
  };
});

export const seedAllData = mutation(async (ctx) => {
  try {
    // Check if any data already exists
    const existingAccounts = await ctx.db.query("accounts").collect();
    const existingCustomers = await ctx.db.query("customers").collect();
    const existingJournalEntries = await ctx.db.query("journalEntries").collect();
    const existingInvoices = await ctx.db.query("invoices").collect();
    const existingPayments = await ctx.db.query("payments").collect();

    if (existingAccounts.length > 0 || existingCustomers.length > 0 ||
        existingJournalEntries.length > 0 || existingInvoices.length > 0 ||
        existingPayments.length > 0) {
      return {
        message: "Data already exists in the database",
        counts: {
          accounts: existingAccounts.length,
          customers: existingCustomers.length,
          journalEntries: existingJournalEntries.length,
          invoices: existingInvoices.length,
          payments: existingPayments.length
        }
      };
    }

    // Enhanced Chart of Accounts for Manufacturing
    const accounts = [
      // Current Assets
      { name: "Cash", type: "Asset" as const, code: "1001", subType: "Current Asset" as const, isActive: true },
      { name: "Accounts Receivable", type: "Asset" as const, code: "1200", subType: "Current Asset" as const, isActive: true },
      { name: "Raw Materials Inventory", type: "Asset" as const, code: "1301", subType: "Inventory" as const, isActive: true },
      { name: "Work in Process Inventory", type: "Asset" as const, code: "1302", subType: "Inventory" as const, isActive: true },
      { name: "Finished Goods Inventory", type: "Asset" as const, code: "1303", subType: "Inventory" as const, isActive: true },
      { name: "Spare Parts Inventory", type: "Asset" as const, code: "1304", subType: "Inventory" as const, isActive: true },
      { name: "Prepaid Insurance", type: "Asset" as const, code: "1400", subType: "Current Asset" as const, isActive: true },

      // Fixed Assets
      { name: "Manufacturing Equipment", type: "Asset" as const, code: "1501", subType: "Fixed Asset" as const, isActive: true },
      { name: "Production Machinery", type: "Asset" as const, code: "1502", subType: "Fixed Asset" as const, isActive: true },
      { name: "Buildings", type: "Asset" as const, code: "1600", subType: "Fixed Asset" as const, isActive: true },
      { name: "Accumulated Depreciation - Equipment", type: "Asset" as const, code: "1551", subType: "Fixed Asset" as const, isActive: true },
      { name: "Accumulated Depreciation - Machinery", type: "Asset" as const, code: "1552", subType: "Fixed Asset" as const, isActive: true },

      // Current Liabilities
      { name: "Accounts Payable", type: "Liability" as const, code: "2001", subType: "Current Liability" as const, isActive: true },
      { name: "Accrued Wages Payable", type: "Liability" as const, code: "2101", subType: "Current Liability" as const, isActive: true },
      { name: "Accrued Expenses", type: "Liability" as const, code: "2200", subType: "Current Liability" as const, isActive: true },
      { name: "Sales Tax Payable", type: "Liability" as const, code: "2300", subType: "Current Liability" as const, isActive: true },

      // Long-term Liabilities
      { name: "Equipment Loan", type: "Liability" as const, code: "2501", subType: "Long-term Liability" as const, isActive: true },
      { name: "Building Mortgage", type: "Liability" as const, code: "2502", subType: "Long-term Liability" as const, isActive: true },

      // Equity
      { name: "Owner's Equity", type: "Equity" as const, code: "3001", isActive: true },
      { name: "Retained Earnings", type: "Equity" as const, code: "3200", isActive: true },

      // Operating Revenue
      { name: "Automotive Parts Sales", type: "Revenue" as const, code: "4001", subType: "Operating Revenue" as const, isActive: true },
      { name: "OEM Sales", type: "Revenue" as const, code: "4002", subType: "Operating Revenue" as const, isActive: true },
      { name: "Aftermarket Sales", type: "Revenue" as const, code: "4003", subType: "Operating Revenue" as const, isActive: true },
      { name: "Service Revenue", type: "Revenue" as const, code: "4100", subType: "Operating Revenue" as const, isActive: true },

      // Non-operating Revenue
      { name: "Interest Income", type: "Revenue" as const, code: "4200", subType: "Non-operating Revenue" as const, isActive: true },

      // Cost of Goods Sold
      { name: "Raw Materials Cost", type: "Expense" as const, code: "5001", subType: "Cost of Goods Sold" as const, isActive: true },
      { name: "Direct Labor Cost", type: "Expense" as const, code: "5002", subType: "Cost of Goods Sold" as const, isActive: true },
      { name: "Manufacturing Overhead", type: "Expense" as const, code: "5003", subType: "Cost of Goods Sold" as const, isActive: true },

      // Operating Expenses
      { name: "Salaries Expense", type: "Expense" as const, code: "6001", subType: "Operating Expense" as const, isActive: true },
      { name: "Factory Rent", type: "Expense" as const, code: "6101", subType: "Operating Expense" as const, isActive: true },
      { name: "Office Rent", type: "Expense" as const, code: "6102", subType: "Operating Expense" as const, isActive: true },
      { name: "Utilities Expense", type: "Expense" as const, code: "6200", subType: "Operating Expense" as const, isActive: true },
      { name: "Maintenance Expense", type: "Expense" as const, code: "6250", subType: "Operating Expense" as const, isActive: true },
      { name: "Quality Control Expense", type: "Expense" as const, code: "6300", subType: "Operating Expense" as const, isActive: true },
      { name: "Marketing Expense", type: "Expense" as const, code: "6400", subType: "Operating Expense" as const, isActive: true },
      { name: "Insurance Expense", type: "Expense" as const, code: "6500", subType: "Operating Expense" as const, isActive: true },
      { name: "Depreciation Expense", type: "Expense" as const, code: "6600", subType: "Operating Expense" as const, isActive: true },

      // Administrative Expenses
      { name: "Administrative Salaries", type: "Expense" as const, code: "7001", subType: "Administrative Expense" as const, isActive: true },
      { name: "Professional Fees", type: "Expense" as const, code: "7100", subType: "Administrative Expense" as const, isActive: true },
      { name: "Office Supplies", type: "Expense" as const, code: "7200", subType: "Administrative Expense" as const, isActive: true },
    ];

    const createdAccounts: Array<{ id: any; name: string; type: string; code: string }> = [];
    for (const account of accounts) {
      const id = await ctx.db.insert("accounts", account);
      createdAccounts.push({ id, ...account });
    }

    // Seed customers
    const customers = [
      {
        name: "ABC Manufacturing Corp",
        contactEmail: "<EMAIL>",
        phone: "******-0101",
        address: "123 Industrial Blvd, Manufacturing City, MC 12345"
      },
      {
        name: "XYZ Retail Solutions",
        contactEmail: "<EMAIL>",
        phone: "******-0102",
        address: "456 Commerce St, Retail Town, RT 67890"
      },
      {
        name: "Global Tech Industries",
        contactEmail: "<EMAIL>",
        phone: "******-0103",
        address: "789 Technology Ave, Tech City, TC 11111"
      },
      {
        name: "Local Services LLC",
        contactEmail: "<EMAIL>",
        phone: "******-0104",
        address: "321 Service Road, Service Village, SV 22222"
      },
      {
        name: "Premium Products Inc",
        contactEmail: "<EMAIL>",
        phone: "******-0105",
        address: "654 Premium Plaza, Luxury Lane, LL 33333"
      }
    ];

    const createdCustomers: Array<{ id: any; name: string; contactEmail: string; phone: string; address: string }> = [];
    for (const customer of customers) {
      const id = await ctx.db.insert("customers", customer);
      createdCustomers.push({ id, ...customer });
    }

    // Add sample journal entries for manufacturing transactions
    const sampleJournalEntries = [
      // Sales transaction
      {
        accountId: createdAccounts.find(acc => acc.code === "1200")?.id,
        date: "2024-01-15",
        description: "Sales to Ford Motor Company - Brake Pads",
        debit: 125000,
        credit: 0,
        reference: "INV-2024-001",
        transactionType: "Sales" as const,
        departmentId: "PROD",
        costCenterId: "PROD001"
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "4001")?.id,
        date: "2024-01-15",
        description: "Sales to Ford Motor Company - Brake Pads",
        debit: 0,
        credit: 125000,
        reference: "INV-2024-001",
        transactionType: "Sales" as const,
        departmentId: "PROD",
        costCenterId: "PROD001"
      },
      // Cost of goods sold
      {
        accountId: createdAccounts.find(acc => acc.code === "5001")?.id,
        date: "2024-01-15",
        description: "Raw materials used in production",
        debit: 45000,
        credit: 0,
        reference: "PROD-001",
        transactionType: "Production" as const,
        departmentId: "PROD",
        costCenterId: "PROD001"
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "1301")?.id,
        date: "2024-01-15",
        description: "Raw materials used in production",
        debit: 0,
        credit: 45000,
        reference: "PROD-001",
        transactionType: "Production" as const,
        departmentId: "PROD",
        costCenterId: "PROD001"
      },
      // Payroll transaction
      {
        accountId: createdAccounts.find(acc => acc.code === "5002")?.id,
        date: "2024-01-31",
        description: "Direct labor costs",
        debit: 35000,
        credit: 0,
        reference: "PAY-2024-01",
        transactionType: "Payroll" as const,
        departmentId: "PROD",
        costCenterId: "PROD001"
      },
      {
        accountId: createdAccounts.find(acc => acc.code === "2101")?.id,
        date: "2024-01-31",
        description: "Direct labor costs",
        debit: 0,
        credit: 35000,
        reference: "PAY-2024-01",
        transactionType: "Payroll" as const,
        departmentId: "PROD",
        costCenterId: "PROD001"
      }
    ];

    const createdJournalEntries: Array<any> = [];
    for (const entry of sampleJournalEntries) {
      if (entry.accountId) {
        const id = await ctx.db.insert("journalEntries", entry);
        createdJournalEntries.push({ id, ...entry });
      }
    }

    return {
      message: "Enhanced manufacturing financial data created successfully",
      counts: {
        accounts: createdAccounts.length,
        customers: createdCustomers.length,
        journalEntries: createdJournalEntries.length,
        invoices: 0,
        payments: 0
      }
    };
  } catch (error) {
    throw new Error(`Failed to seed data: ${error}`);
  }
});

export const getIncomeStatement = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  const revenueAccounts = accounts.filter((acc) => acc.type === "Revenue");
  const expenseAccounts = accounts.filter((acc) => acc.type === "Expense");

  const revenueData = revenueAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.credit - entry.debit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const expenseData = expenseAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.debit - entry.credit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const totalRevenue = revenueData.reduce((sum, item) => sum + item.balance, 0);
  const totalExpenses = expenseData.reduce((sum, item) => sum + item.balance, 0);
  const netIncome = totalRevenue - totalExpenses;

  return {
    revenue: revenueData,
    expenses: expenseData,
    totalRevenue,
    totalExpenses,
    netIncome
  };
});

export const getBalanceSheet = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  const assetAccounts = accounts.filter((acc) => acc.type === "Asset");
  const liabilityAccounts = accounts.filter((acc) => acc.type === "Liability");
  const equityAccounts = accounts.filter((acc) => acc.type === "Equity");

  const assets = assetAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.debit - entry.credit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const liabilities = liabilityAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.credit - entry.debit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const equity = equityAccounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const balance = entries.reduce((total, entry) => total + (entry.credit - entry.debit), 0);
    return {
      accountName: account.name,
      accountCode: account.code,
      balance
    };
  });

  const totalAssets = assets.reduce((sum, item) => sum + item.balance, 0);
  const totalLiabilities = liabilities.reduce((sum, item) => sum + item.balance, 0);
  const totalEquity = equity.reduce((sum, item) => sum + item.balance, 0);

  return {
    assets,
    liabilities,
    equity,
    totalAssets,
    totalLiabilities,
    totalEquity
  };
});

export const getTrialBalance = query(async (ctx) => {
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();

  const trialBalance = accounts.map(account => {
    const entries = journalEntries.filter(entry => entry.accountId === account._id);
    const totalDebits = entries.reduce((sum, entry) => sum + entry.debit, 0);
    const totalCredits = entries.reduce((sum, entry) => sum + entry.credit, 0);

    let debitBalance = 0;
    let creditBalance = 0;

    if (account.type === "Asset" || account.type === "Expense") {
      const balance = totalDebits - totalCredits;
      if (balance > 0) debitBalance = balance;
      else creditBalance = Math.abs(balance);
    } else {
      const balance = totalCredits - totalDebits;
      if (balance > 0) creditBalance = balance;
      else debitBalance = Math.abs(balance);
    }

    return {
      accountName: account.name,
      accountCode: account.code,
      accountType: account.type,
      debitBalance,
      creditBalance
    };
  }).filter(account => account.debitBalance > 0 || account.creditBalance > 0);

  const totalDebits = trialBalance.reduce((sum, item) => sum + item.debitBalance, 0);
  const totalCredits = trialBalance.reduce((sum, item) => sum + item.creditBalance, 0);

  return {
    accounts: trialBalance,
    totalDebits,
    totalCredits,
    isBalanced: Math.abs(totalDebits - totalCredits) < 0.01
  };
});

export const getAgingReport = query(async (ctx) => {
  const invoices = await ctx.db.query("invoices").collect();
  const customers = await ctx.db.query("customers").collect();
  const payments = await ctx.db.query("payments").collect();

  const today = new Date();

  const agingData = invoices
    .filter(invoice => invoice.status !== "Paid")
    .map(invoice => {
      const customer = customers.find(c => c._id === invoice.customerId);
      const dueDate = new Date(invoice.dueDate);
      const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

      let ageCategory = "Current";
      if (daysPastDue > 0 && daysPastDue <= 30) ageCategory = "1-30 Days";
      else if (daysPastDue > 30 && daysPastDue <= 60) ageCategory = "31-60 Days";
      else if (daysPastDue > 60 && daysPastDue <= 90) ageCategory = "61-90 Days";
      else if (daysPastDue > 90) ageCategory = "Over 90 Days";

      return {
        customerName: customer?.name || "Unknown Customer",
        invoiceNumber: invoice.invoiceNumber,
        dateIssued: invoice.dateIssued,
        dueDate: invoice.dueDate,
        amount: invoice.totalAmount,
        daysPastDue,
        ageCategory,
        status: invoice.status
      };
    });

  const summary = {
    current: agingData.filter(item => item.ageCategory === "Current").reduce((sum, item) => sum + item.amount, 0),
    days1to30: agingData.filter(item => item.ageCategory === "1-30 Days").reduce((sum, item) => sum + item.amount, 0),
    days31to60: agingData.filter(item => item.ageCategory === "31-60 Days").reduce((sum, item) => sum + item.amount, 0),
    days61to90: agingData.filter(item => item.ageCategory === "61-90 Days").reduce((sum, item) => sum + item.amount, 0),
    over90Days: agingData.filter(item => item.ageCategory === "Over 90 Days").reduce((sum, item) => sum + item.amount, 0)
  };

  const totalOutstanding = Object.values(summary).reduce((sum, amount) => sum + amount, 0);

  return {
    invoices: agingData,
    summary,
    totalOutstanding
  };
});

// Enhanced Financial Functions for Manufacturing

export const getCostCenterReport = query(async (ctx) => {
  const costCenters = await ctx.db.query("costCenters").collect();
  const budgets = await ctx.db.query("budgets").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();
  const employees = await ctx.db.query("employees").collect();

  const costCenterData = costCenters.map(center => {
    const centerBudgets = budgets.filter(budget => budget.costCenterId === center._id);
    const manager = employees.find(emp => emp._id === center.managerId);

    const totalBudgeted = centerBudgets.reduce((sum, budget) => sum + budget.budgetedAmount, 0);
    const totalActual = centerBudgets.reduce((sum, budget) => sum + budget.actualAmount, 0);
    const variance = totalBudgeted - totalActual;
    const variancePercentage = totalBudgeted > 0 ? (variance / totalBudgeted) * 100 : 0;

    return {
      ...center,
      managerName: manager ? `${manager.firstName} ${manager.lastName}` : "Unassigned",
      totalBudgeted,
      totalActual,
      variance,
      variancePercentage,
      budgetCount: centerBudgets.length
    };
  });

  return {
    costCenters: costCenterData,
    summary: {
      totalCostCenters: costCenters.length,
      activeCostCenters: costCenters.filter(cc => cc.isActive).length,
      totalBudgetAmount: costCenterData.reduce((sum, cc) => sum + cc.totalBudgeted, 0),
      totalActualAmount: costCenterData.reduce((sum, cc) => sum + cc.totalActual, 0)
    }
  };
});

export const getFixedAssetsReport = query(async (ctx) => {
  const fixedAssets = await ctx.db.query("fixedAssets").collect();
  const accounts = await ctx.db.query("accounts").collect();

  const assetData = fixedAssets.map(asset => {
    const ageInYears = (new Date().getTime() - new Date(asset.purchaseDate).getTime()) / (1000 * 60 * 60 * 24 * 365);
    const depreciationRate = asset.usefulLife > 0 ? 1 / asset.usefulLife : 0;
    const annualDepreciation = (asset.purchaseCost - asset.salvageValue) * depreciationRate;

    return {
      ...asset,
      ageInYears: Math.round(ageInYears * 10) / 10,
      annualDepreciation,
      depreciationRate: depreciationRate * 100,
      remainingLife: Math.max(0, asset.usefulLife - ageInYears)
    };
  });

  const categoryBreakdown = {
    machinery: assetData.filter(asset => asset.category === "Machinery"),
    equipment: assetData.filter(asset => asset.category === "Equipment"),
    building: assetData.filter(asset => asset.category === "Building"),
    vehicle: assetData.filter(asset => asset.category === "Vehicle"),
    itEquipment: assetData.filter(asset => asset.category === "IT Equipment")
  };

  return {
    assets: assetData,
    categoryBreakdown,
    summary: {
      totalAssets: fixedAssets.length,
      totalPurchaseCost: fixedAssets.reduce((sum, asset) => sum + asset.purchaseCost, 0),
      totalCurrentValue: fixedAssets.reduce((sum, asset) => sum + asset.currentBookValue, 0),
      totalDepreciation: fixedAssets.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0),
      activeAssets: fixedAssets.filter(asset => asset.status === "Active").length
    }
  };
});

export const getAccountsPayableReport = query(async (ctx) => {
  const vendorInvoices = await ctx.db.query("vendorInvoices").collect();
  const suppliers = await ctx.db.query("suppliers").collect();
  const purchaseOrders = await ctx.db.query("purchaseOrders").collect();

  const payableData = vendorInvoices.map(invoice => {
    const supplier = suppliers.find(s => s._id === invoice.supplierId);
    const purchaseOrder = purchaseOrders.find(po => po._id === invoice.purchaseOrderId);
    const dueDate = new Date(invoice.dueDate);
    const today = new Date();
    const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      ...invoice,
      supplierName: supplier?.name || "Unknown Supplier",
      purchaseOrderNumber: purchaseOrder?.orderNumber || "N/A",
      daysPastDue: Math.max(0, daysPastDue),
      outstandingAmount: invoice.totalAmount - invoice.paidAmount
    };
  });

  const statusBreakdown = {
    pending: payableData.filter(inv => inv.status === "Pending"),
    approved: payableData.filter(inv => inv.status === "Approved"),
    paid: payableData.filter(inv => inv.status === "Paid"),
    overdue: payableData.filter(inv => inv.status === "Overdue")
  };

  // Create aging report by supplier
  const agingReport = suppliers.map(supplier => {
    const supplierInvoices = payableData.filter(inv => inv.supplierId === supplier._id);

    const current = supplierInvoices.filter(inv => inv.daysPastDue === 0).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days1to30 = supplierInvoices.filter(inv => inv.daysPastDue > 0 && inv.daysPastDue <= 30).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days31to60 = supplierInvoices.filter(inv => inv.daysPastDue > 30 && inv.daysPastDue <= 60).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days61to90 = supplierInvoices.filter(inv => inv.daysPastDue > 60 && inv.daysPastDue <= 90).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days90Plus = supplierInvoices.filter(inv => inv.daysPastDue > 90).reduce((sum, inv) => sum + inv.outstandingAmount, 0);

    return {
      supplierId: supplier._id,
      supplierName: supplier.name,
      current,
      days1to30,
      days31to60,
      days61to90,
      days90Plus,
      total: current + days1to30 + days31to60 + days61to90 + days90Plus
    };
  }).filter(supplier => supplier.total > 0);

  return {
    payables: payableData,
    agingReport,
    statusBreakdown,
    summary: {
      totalBills: vendorInvoices.length,
      totalAmount: vendorInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0),
      totalPaid: vendorInvoices.reduce((sum, inv) => sum + inv.paidAmount, 0),
      totalOutstanding: vendorInvoices.reduce((sum, inv) => sum + (inv.totalAmount - inv.paidAmount), 0),
      overdueAmount: payableData.filter(inv => inv.daysPastDue > 0).reduce((sum, inv) => sum + inv.outstandingAmount, 0),
      overdueCount: statusBreakdown.overdue.length,
      averageDaysOutstanding: payableData.length > 0 ? payableData.reduce((sum, inv) => sum + inv.daysPastDue, 0) / payableData.length : 0
    }
  };
});

export const getCustomers = query(async (ctx) => {
  return await ctx.db.query("customers").collect();
});

export const getSuppliers = query(async (ctx) => {
  return await ctx.db.query("suppliers").collect();
});

export const getAccountsReceivableReport = query(async (ctx) => {
  const customerInvoices = await ctx.db.query("customerInvoices").collect();
  const customers = await ctx.db.query("customers").collect();

  const receivableData = customerInvoices.map(invoice => {
    const customer = customers.find(c => c._id === invoice.customerId);
    const dueDate = new Date(invoice.dueDate);
    const today = new Date();
    const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      ...invoice,
      customerName: customer?.name || "Unknown Customer",
      customerType: customer?.customerType || "Unknown",
      daysPastDue: Math.max(0, daysPastDue),
      outstandingAmount: invoice.totalAmount - invoice.paidAmount
    };
  });

  const statusBreakdown = {
    draft: receivableData.filter(inv => inv.status === "Draft"),
    sent: receivableData.filter(inv => inv.status === "Sent"),
    paid: receivableData.filter(inv => inv.status === "Paid"),
    overdue: receivableData.filter(inv => inv.status === "Overdue"),
    cancelled: receivableData.filter(inv => inv.status === "Cancelled")
  };

  const customerTypeBreakdown = {
    oem: receivableData.filter(inv => inv.customerType === "OEM"),
    aftermarket: receivableData.filter(inv => inv.customerType === "Aftermarket"),
    distributor: receivableData.filter(inv => inv.customerType === "Distributor")
  };

  // Create aging report by customer
  const agingReport = customers.map(customer => {
    const customerInvoices = receivableData.filter(inv => inv.customerId === customer._id);

    const current = customerInvoices.filter(inv => inv.daysPastDue === 0).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days1to30 = customerInvoices.filter(inv => inv.daysPastDue > 0 && inv.daysPastDue <= 30).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days31to60 = customerInvoices.filter(inv => inv.daysPastDue > 30 && inv.daysPastDue <= 60).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days61to90 = customerInvoices.filter(inv => inv.daysPastDue > 60 && inv.daysPastDue <= 90).reduce((sum, inv) => sum + inv.outstandingAmount, 0);
    const days90Plus = customerInvoices.filter(inv => inv.daysPastDue > 90).reduce((sum, inv) => sum + inv.outstandingAmount, 0);

    return {
      customerId: customer._id,
      customerName: customer.name,
      current,
      days1to30,
      days31to60,
      days61to90,
      days90Plus,
      total: current + days1to30 + days31to60 + days61to90 + days90Plus
    };
  }).filter(customer => customer.total > 0);

  return {
    receivables: receivableData,
    agingReport,
    statusBreakdown,
    customerTypeBreakdown,
    summary: {
      totalInvoices: customerInvoices.length,
      totalAmount: customerInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0),
      totalPaid: customerInvoices.reduce((sum, inv) => sum + inv.paidAmount, 0),
      totalOutstanding: customerInvoices.reduce((sum, inv) => sum + (inv.totalAmount - inv.paidAmount), 0),
      overdueAmount: receivableData.filter(inv => inv.daysPastDue > 0).reduce((sum, inv) => sum + inv.outstandingAmount, 0),
      overdueCount: statusBreakdown.overdue.length,
      averageDaysOutstanding: receivableData.length > 0 ? receivableData.reduce((sum, inv) => sum + inv.daysPastDue, 0) / receivableData.length : 0
    }
  };
});

export const getManufacturingCostReport = query(async (ctx) => {
  const workOrderCosts = await ctx.db.query("workOrderCosts").collect();
  const workOrders = await ctx.db.query("workOrders").collect();
  const productionOrders = await ctx.db.query("productionOrders").collect();
  const inventory = await ctx.db.query("inventory").collect();
  const accounts = await ctx.db.query("accounts").collect();

  const costData = workOrderCosts.map(cost => {
    const workOrder = workOrders.find(wo => wo._id === cost.workOrderId);
    const productionOrder = productionOrders.find(po => po._id === workOrder?.productionOrderId);
    const product = inventory.find(inv => inv._id === productionOrder?.productId);
    const account = accounts.find(acc => acc._id === cost.accountId);

    return {
      ...cost,
      workOrderNumber: workOrder?.workOrderNumber || "N/A",
      productionOrderNumber: productionOrder?.orderNumber || "N/A",
      productName: product?.partName || "Unknown Product",
      accountName: account?.name || "Unknown Account"
    };
  });

  const costByType = {
    material: costData.filter(cost => cost.costType === "Material"),
    labor: costData.filter(cost => cost.costType === "Labor"),
    overhead: costData.filter(cost => cost.costType === "Overhead")
  };

  const totalCosts = {
    material: costByType.material.reduce((sum, cost) => sum + cost.amount, 0),
    labor: costByType.labor.reduce((sum, cost) => sum + cost.amount, 0),
    overhead: costByType.overhead.reduce((sum, cost) => sum + cost.amount, 0)
  };

  return {
    costs: costData,
    costByType,
    totalCosts,
    summary: {
      totalManufacturingCost: Object.values(totalCosts).reduce((sum, cost) => sum + cost, 0),
      materialPercentage: totalCosts.material / (Object.values(totalCosts).reduce((sum, cost) => sum + cost, 0) || 1) * 100,
      laborPercentage: totalCosts.labor / (Object.values(totalCosts).reduce((sum, cost) => sum + cost, 0) || 1) * 100,
      overheadPercentage: totalCosts.overhead / (Object.values(totalCosts).reduce((sum, cost) => sum + cost, 0) || 1) * 100
    }
  };
});