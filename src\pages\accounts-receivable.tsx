import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function AccountsReceivable() {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <h1 className="text-3xl font-bold mb-6">Accounts Receivable</h1>
          <div className="bg-white p-8 rounded-lg shadow-md">
            <p className="text-gray-600">
              This page will manage customer invoices, payments, and outstanding receivables.
            </p>
            <div className="mt-4 text-sm text-gray-500">
              Features to be implemented:
              <ul className="list-disc list-inside mt-2">
                <li>View outstanding invoices</li>
                <li>Create new customer invoices</li>
                <li>Record customer payments</li>
                <li>Customer management</li>
                <li>Aging reports</li>
                <li>Payment reminders</li>
              </ul>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default AccountsReceivable;
