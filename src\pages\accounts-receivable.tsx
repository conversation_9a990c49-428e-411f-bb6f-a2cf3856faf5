import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function AccountsReceivable() {
  const [activeTab, setActiveTab] = useState<'overview' | 'invoices' | 'customers' | 'aging'>('overview');
  const [showNewInvoiceModal, setShowNewInvoiceModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);

  // Fetch data
  const receivableData = useQuery(api.tasks.getAccountsReceivableReport);
  const customers = useQuery(api.tasks.getCustomers);
  const dashboardData = useQuery(api.tasks.getDashboardData);

  if (!receivableData || !customers || !dashboardData) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Accounts Receivable</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  const { receivables, agingReport, summary } = receivableData;

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Accounts Receivable</h1>
            <button
              onClick={() => setShowNewInvoiceModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Invoice
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-4 mb-6">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'invoices', label: 'Invoices' },
              { key: 'customers', label: 'Customers' },
              { key: 'aging', label: 'Aging Report' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Total Outstanding</p>
                      <p className="text-3xl font-bold text-blue-600">
                        ${summary.totalOutstanding.toLocaleString()}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Overdue Amount</p>
                      <p className="text-3xl font-bold text-red-600">
                        ${summary.overdueAmount.toLocaleString()}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Total Invoices</p>
                      <p className="text-3xl font-bold text-green-600">
                        {summary.totalInvoices}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Avg Days Outstanding</p>
                      <p className="text-3xl font-bold text-purple-600">
                        {Math.round(summary.averageDaysOutstanding)}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Invoices */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Outstanding Invoices</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice #</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Due Date</th>
                        <th className="text-right py-3 px-4 font-semibold text-gray-700">Amount</th>
                        <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
                        <th className="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {receivables.slice(0, 5).map((invoice) => (
                        <tr key={invoice._id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4 font-medium text-blue-600">{invoice.invoiceNumber}</td>
                          <td className="py-4 px-4">{invoice.customerName}</td>
                          <td className="py-4 px-4">
                            <span className={`${
                              invoice.daysPastDue > 0 ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {new Date(invoice.dueDate).toLocaleDateString()}
                            </span>
                          </td>
                          <td className="py-4 px-4 text-right font-medium">
                            ${invoice.outstandingAmount.toLocaleString()}
                          </td>
                          <td className="py-4 px-4 text-center">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                              invoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {invoice.status}
                            </span>
                          </td>
                          <td className="py-4 px-4 text-center">
                            <button
                              onClick={() => {
                                setSelectedInvoice(invoice);
                                setShowPaymentModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                              Record Payment
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Invoices Tab */}
          {activeTab === 'invoices' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">All Customer Invoices</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice #</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Issue Date</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Due Date</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Total Amount</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Outstanding</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">Days Past Due</th>
                    </tr>
                  </thead>
                  <tbody>
                    {receivables.map((invoice) => (
                      <tr key={invoice._id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4 font-medium text-blue-600">{invoice.invoiceNumber}</td>
                        <td className="py-4 px-4">
                          <div>
                            <div className="font-medium">{invoice.customerName}</div>
                            <div className="text-sm text-gray-500">{invoice.customerType}</div>
                          </div>
                        </td>
                        <td className="py-4 px-4">{new Date(invoice.invoiceDate).toLocaleDateString()}</td>
                        <td className="py-4 px-4">
                          <span className={`${
                            invoice.daysPastDue > 0 ? 'text-red-600 font-medium' : 'text-gray-600'
                          }`}>
                            {new Date(invoice.dueDate).toLocaleDateString()}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-right font-medium">
                          ${invoice.totalAmount.toLocaleString()}
                        </td>
                        <td className="py-4 px-4 text-right font-medium">
                          ${invoice.outstandingAmount.toLocaleString()}
                        </td>
                        <td className="py-4 px-4 text-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                            invoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {invoice.status}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-center">
                          <span className={`font-medium ${
                            invoice.daysPastDue > 30 ? 'text-red-600' :
                            invoice.daysPastDue > 0 ? 'text-orange-600' :
                            'text-gray-600'
                          }`}>
                            {invoice.daysPastDue > 0 ? invoice.daysPastDue : '-'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Customers Tab */}
          {activeTab === 'customers' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Customer Management</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer Name</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Contact</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit Limit</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Payment Terms</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Outstanding Balance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customers.map((customer) => {
                      const customerBalance = receivables
                        .filter(inv => inv.customerId === customer._id)
                        .reduce((sum, inv) => sum + inv.outstandingAmount, 0);

                      return (
                        <tr key={customer._id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4 font-medium">{customer.name}</td>
                          <td className="py-4 px-4">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {customer.customerType || 'Standard'}
                            </span>
                          </td>
                          <td className="py-4 px-4">
                            <div>
                              <div className="text-sm">{customer.contactEmail}</div>
                              {customer.phone && <div className="text-xs text-gray-500">{customer.phone}</div>}
                            </div>
                          </td>
                          <td className="py-4 px-4 text-right">
                            ${(customer.creditLimit || 0).toLocaleString()}
                          </td>
                          <td className="py-4 px-4">{customer.paymentTerms || 'Net 30'}</td>
                          <td className="py-4 px-4 text-right font-medium">
                            <span className={customerBalance > 0 ? 'text-red-600' : 'text-green-600'}>
                              ${customerBalance.toLocaleString()}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Aging Report Tab */}
          {activeTab === 'aging' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Accounts Receivable Aging Report</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Current</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">1-30 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">31-60 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">61-90 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">90+ Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {agingReport.map((customer) => (
                      <tr key={customer.customerId} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4 font-medium">{customer.customerName}</td>
                        <td className="py-4 px-4 text-right">${customer.current.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-yellow-600">${customer.days1to30.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-orange-600">${customer.days31to60.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-red-600">${customer.days61to90.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-red-700 font-medium">${customer.days90Plus.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right font-bold">${customer.total.toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

export default AccountsReceivable;
