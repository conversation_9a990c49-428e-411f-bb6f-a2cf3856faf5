import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function AccountsPayable() {
  const [activeTab, setActiveTab] = useState<'overview' | 'bills' | 'suppliers' | 'aging'>('overview');
  const [showNewBillModal, setShowNewBillModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedBill, setSelectedBill] = useState<any>(null);

  // Fetch data
  const payableData = useQuery(api.tasks.getAccountsPayableReport);
  const suppliers = useQuery(api.tasks.getSuppliers);
  const dashboardData = useQuery(api.tasks.getDashboardData);

  if (!payableData || !suppliers || !dashboardData) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Accounts Payable</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  const { payables, agingReport, summary } = payableData;

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gradient-to-br from-teal-50 to-gray-100 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Accounts Payable</h1>
            <button
              type="button"
              onClick={() => setShowNewBillModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Record Bill
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-4 mb-6">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'bills', label: 'Bills' },
              { key: 'suppliers', label: 'Suppliers' },
              { key: 'aging', label: 'Aging Report' }
            ].map((tab) => (
              <button
                key={tab.key}
                type="button"
                onClick={() => setActiveTab(tab.key as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Total Outstanding</p>
                      <p className="text-3xl font-bold text-red-600">
                        ${summary.totalOutstanding.toLocaleString()}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Overdue Amount</p>
                      <p className="text-3xl font-bold text-orange-600">
                        ${summary.overdueAmount.toLocaleString()}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Total Bills</p>
                      <p className="text-3xl font-bold text-blue-600">
                        {summary.totalBills}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Avg Days Outstanding</p>
                      <p className="text-3xl font-bold text-purple-600">
                        {Math.round(summary.averageDaysOutstanding)}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Bills */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Outstanding Bills</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice #</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Supplier</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Due Date</th>
                        <th className="text-right py-3 px-4 font-semibold text-gray-700">Amount</th>
                        <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
                        <th className="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {payables.slice(0, 5).map((bill) => (
                        <tr key={bill._id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4 font-medium text-blue-600">{bill.invoiceNumber}</td>
                          <td className="py-4 px-4">{bill.supplierName}</td>
                          <td className="py-4 px-4">
                            <span className={`${
                              bill.daysPastDue > 0 ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {new Date(bill.dueDate).toLocaleDateString()}
                            </span>
                          </td>
                          <td className="py-4 px-4 text-right font-medium">
                            ${bill.outstandingAmount.toLocaleString()}
                          </td>
                          <td className="py-4 px-4 text-center">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              bill.status === 'Paid' ? 'bg-green-100 text-green-800' :
                              bill.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {bill.status}
                            </span>
                          </td>
                          <td className="py-4 px-4 text-center">
                            <button
                              type="button"
                              onClick={() => {
                                setSelectedBill(bill);
                                setShowPaymentModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                              Pay Bill
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Bills Tab */}
          {activeTab === 'bills' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">All Vendor Bills</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice #</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Supplier</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice Date</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Due Date</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Total Amount</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Outstanding</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">Days Past Due</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">PO #</th>
                    </tr>
                  </thead>
                  <tbody>
                    {payables.map((bill) => (
                      <tr key={bill._id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4 font-medium text-blue-600">{bill.invoiceNumber}</td>
                        <td className="py-4 px-4">
                          <div className="font-medium">{bill.supplierName}</div>
                        </td>
                        <td className="py-4 px-4">{new Date(bill.invoiceDate).toLocaleDateString()}</td>
                        <td className="py-4 px-4">
                          <span className={`${
                            bill.daysPastDue > 0 ? 'text-red-600 font-medium' : 'text-gray-600'
                          }`}>
                            {new Date(bill.dueDate).toLocaleDateString()}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-right font-medium">
                          ${bill.totalAmount.toLocaleString()}
                        </td>
                        <td className="py-4 px-4 text-right font-medium">
                          ${bill.outstandingAmount.toLocaleString()}
                        </td>
                        <td className="py-4 px-4 text-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            bill.status === 'Paid' ? 'bg-green-100 text-green-800' :
                            bill.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                            bill.status === 'Approved' ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {bill.status}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-center">
                          <span className={`font-medium ${
                            bill.daysPastDue > 30 ? 'text-red-600' :
                            bill.daysPastDue > 0 ? 'text-orange-600' :
                            'text-gray-600'
                          }`}>
                            {bill.daysPastDue > 0 ? bill.daysPastDue : '-'}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-sm text-gray-500">
                          {bill.purchaseOrderNumber || 'N/A'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Suppliers Tab */}
          {activeTab === 'suppliers' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Supplier Management</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Supplier Name</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Contact</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Payment Terms</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">Quality Rating</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Outstanding Balance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {suppliers.map((supplier) => {
                      const supplierBalance = payables
                        .filter(bill => bill.supplierId === supplier._id)
                        .reduce((sum, bill) => sum + bill.outstandingAmount, 0);

                      return (
                        <tr key={supplier._id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4 font-medium">{supplier.name}</td>
                          <td className="py-4 px-4">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              {supplier.supplierType}
                            </span>
                          </td>
                          <td className="py-4 px-4">
                            <div>
                              <div className="text-sm">{supplier.contactEmail}</div>
                              {supplier.phone && <div className="text-xs text-gray-500">{supplier.phone}</div>}
                            </div>
                          </td>
                          <td className="py-4 px-4">{supplier.paymentTerms}</td>
                          <td className="py-4 px-4 text-center">
                            <div className="flex items-center justify-center">
                              <span className="text-yellow-400">
                                {'★'.repeat(supplier.qualityRating)}
                              </span>
                              <span className="text-gray-300">
                                {'★'.repeat(5 - supplier.qualityRating)}
                              </span>
                              <span className="ml-2 text-sm text-gray-600">
                                ({supplier.qualityRating}/5)
                              </span>
                            </div>
                          </td>
                          <td className="py-4 px-4 text-right font-medium">
                            <span className={supplierBalance > 0 ? 'text-red-600' : 'text-green-600'}>
                              ${supplierBalance.toLocaleString()}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Aging Report Tab */}
          {activeTab === 'aging' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Accounts Payable Aging Report</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Supplier</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Current</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">1-30 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">31-60 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">61-90 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">90+ Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {agingReport.map((supplier) => (
                      <tr key={supplier.supplierId} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4 font-medium">{supplier.supplierName}</td>
                        <td className="py-4 px-4 text-right">${supplier.current.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-yellow-600">${supplier.days1to30.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-orange-600">${supplier.days31to60.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-red-600">${supplier.days61to90.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-red-700 font-medium">${supplier.days90Plus.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right font-bold">${supplier.total.toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

export default AccountsPayable;
