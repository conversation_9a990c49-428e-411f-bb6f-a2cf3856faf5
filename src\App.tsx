import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './pages/dashboard';
import AccountsPayable from './pages/accounts-payable';
import AccountsReceivable from './pages/accounts-receivable';
import Reports from './pages/reports';
import Users from './pages/users';
import Notifications from './pages/notifications';
import Settings from './pages/settings';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/accounts-payable" element={<AccountsPayable />} />
        <Route path="/accounts-receivable" element={<AccountsReceivable />} />
        <Route path="/reports" element={<Reports />} />
        <Route path="/users" element={<Users />} />
        <Route path="/notifications" element={<Notifications />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Router>
  );
}

export default App;
