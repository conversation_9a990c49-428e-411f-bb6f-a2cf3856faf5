import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './pages/dashboard';
import AccountsPayable from './pages/accounts-payable';
import AccountsReceivable from './pages/accounts-receivable';
import Reports from './pages/reports';
import Users from './pages/users';
import Notifications from './pages/notifications';
import Settings from './pages/settings';
import ChartOfAccounts from './pages/chart-of-accounts';
import Customers from './pages/customers';
import Suppliers from './pages/suppliers';
import JournalEntries from './pages/journal-entries';
import TrialBalance from './pages/trial-balance';
import GeneralLedger from './pages/general-ledger';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/accounts-payable" element={<AccountsPayable />} />
        <Route path="/accounts-receivable" element={<AccountsReceivable />} />
        <Route path="/reports" element={<Reports />} />
        <Route path="/users" element={<Users />} />
        <Route path="/notifications" element={<Notifications />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/chart-of-accounts" element={<ChartOfAccounts />} />
        <Route path="/customers" element={<Customers />} />
        <Route path="/suppliers" element={<Suppliers />} />
        <Route path="/journal-entries" element={<JournalEntries />} />
        <Route path="/trial-balance" element={<TrialBalance />} />
        <Route path="/general-ledger" element={<GeneralLedger />} />
      </Routes>
    </Router>
  );
}

export default App;
