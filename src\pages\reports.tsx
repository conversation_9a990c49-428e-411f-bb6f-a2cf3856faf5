import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function Reports() {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <h1 className="text-3xl font-bold mb-6">Financial Reports</h1>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Income Statement</h3>
              <p className="text-gray-600 text-sm mb-4">
                View revenue, expenses, and profit/loss over a period.
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">
                Generate Report
              </button>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Balance Sheet</h3>
              <p className="text-gray-600 text-sm mb-4">
                View assets, liabilities, and equity at a point in time.
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">
                Generate Report
              </button>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Cash Flow Statement</h3>
              <p className="text-gray-600 text-sm mb-4">
                Track cash inflows and outflows over a period.
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">
                Generate Report
              </button>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Aging Report</h3>
              <p className="text-gray-600 text-sm mb-4">
                View outstanding receivables by age.
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">
                Generate Report
              </button>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Trial Balance</h3>
              <p className="text-gray-600 text-sm mb-4">
                View all account balances to ensure books balance.
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">
                Generate Report
              </button>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">General Ledger</h3>
              <p className="text-gray-600 text-sm mb-4">
                View detailed transaction history by account.
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">
                Generate Report
              </button>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Reports;
