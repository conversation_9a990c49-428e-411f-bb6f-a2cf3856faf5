// convex/finance/getDashboardData.ts
import { query } from "../_generated/server";
import { v } from "convex/values";

export const getDashboardData = query(async (ctx) => {
  // Fetch all accounts to identify Revenue and Expense account IDs
  const accounts = await ctx.db.query("accounts").collect();
  const journalEntries = await ctx.db.query("journalEntries").collect();
  const invoices = await ctx.db.query("invoices").collect();
  const customers = await ctx.db.query("customers").collect();
  const payments = await ctx.db.query("payments").collect();

  const revenueAccounts = accounts.filter((acc) => acc.type === "Revenue");
  const expenseAccounts = accounts.filter((acc) => acc.type === "Expense");
  const assetAccounts = accounts.filter((acc) => acc.type === "Asset");
  const liabilityAccounts = accounts.filter((acc) => acc.type === "Liability");

  const revenueAccountIds = revenueAccounts.map((acc) => acc._id);
  const expenseAccountIds = expenseAccounts.map((acc) => acc._id);
  const assetAccountIds = assetAccounts.map((acc) => acc._id);
  const liabilityAccountIds = liabilityAccounts.map((acc) => acc._id);

  // Calculate revenue and expenses
  const revenueEntries = journalEntries.filter((entry) =>
    revenueAccountIds.includes(entry.accountId)
  );

  const expenseEntries = journalEntries.filter((entry) =>
    expenseAccountIds.includes(entry.accountId)
  );

  const assetEntries = journalEntries.filter((entry) =>
    assetAccountIds.includes(entry.accountId)
  );

  const liabilityEntries = journalEntries.filter((entry) =>
    liabilityAccountIds.includes(entry.accountId)
  );

  // Sum credit - debit for revenue (credit increases revenue)
  const revenue = revenueEntries.reduce((total, entry) => {
    return total + (entry.credit - entry.debit);
  }, 0);

  // Sum debit - credit for expenses (debit increases expenses)
  const expenses = expenseEntries.reduce((total, entry) => {
    return total + (entry.debit - entry.credit);
  }, 0);

  // Sum debit - credit for assets (debit increases assets)
  const totalAssets = assetEntries.reduce((total, entry) => {
    return total + (entry.debit - entry.credit);
  }, 0);

  // Sum credit - debit for liabilities (credit increases liabilities)
  const totalLiabilities = liabilityEntries.reduce((total, entry) => {
    return total + (entry.credit - entry.debit);
  }, 0);

  // Calculate invoice metrics
  const totalInvoices = invoices.length;
  const paidInvoices = invoices.filter(inv => inv.status === "Paid").length;
  const pendingInvoices = invoices.filter(inv => inv.status === "Pending").length;
  const overdueInvoices = invoices.filter(inv => inv.status === "Overdue").length;

  const totalInvoiceAmount = invoices.reduce((sum, inv) => sum + inv.totalAmount, 0);
  const paidInvoiceAmount = invoices
    .filter(inv => inv.status === "Paid")
    .reduce((sum, inv) => sum + inv.totalAmount, 0);
  const outstandingInvoiceAmount = totalInvoiceAmount - paidInvoiceAmount;

  // Get recent transactions (last 5)
  const recentTransactions = journalEntries
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5)
    .map(entry => {
      const account = accounts.find(acc => acc._id === entry.accountId);
      return {
        ...entry,
        accountName: account?.name || "Unknown Account",
        accountCode: account?.code || "N/A"
      };
    });

  // Calculate total payments received
  const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);

  return {
    revenue,
    expenses,
    profit: revenue - expenses,
    totalAssets,
    totalLiabilities,
    netWorth: totalAssets - totalLiabilities,
    totalInvoices,
    paidInvoices,
    pendingInvoices,
    overdueInvoices,
    totalInvoiceAmount,
    paidInvoiceAmount,
    outstandingInvoiceAmount,
    totalPayments,
    totalCustomers: customers.length,
    recentTransactions,
    accountsSummary: {
      totalAccounts: accounts.length,
      revenueAccounts: revenueAccounts.length,
      expenseAccounts: expenseAccounts.length,
      assetAccounts: assetAccounts.length,
      liabilityAccounts: liabilityAccounts.length,
    }
  };
});