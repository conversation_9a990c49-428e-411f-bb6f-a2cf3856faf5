import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function ChartOfAccounts() {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAccount, setEditingAccount] = useState<any>(null);
  const [formData, setFormData] = useState<{
    name: string;
    code: string;
    type: "Asset" | "Liability" | "Equity" | "Revenue" | "Expense";
    subType: "Current Asset" | "Fixed Asset" | "Inventory" | "Current Liability" | "Long-term Liability" | "Operating Revenue" | "Non-operating Revenue" | "Cost of Goods Sold" | "Operating Expense" | "Administrative Expense" | "";
    isActive: boolean;
  }>({
    name: "",
    code: "",
    type: "Asset",
    subType: "",
    isActive: true
  });

  const accounts = useQuery(api.tasks.getAccounts);
  const addAccount = useMutation(api.tasks.addAccount);
  const updateAccount = useMutation(api.tasks.updateAccount);
  const deleteAccount = useMutation(api.tasks.deleteAccount);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const submitData = {
        name: formData.name,
        code: formData.code,
        type: formData.type,
        subType: formData.subType || undefined,
        isActive: formData.isActive
      };

      if (editingAccount) {
        await updateAccount({
          id: editingAccount._id,
          ...submitData
        });
      } else {
        await addAccount(submitData);
      }
      setShowAddModal(false);
      setEditingAccount(null);
      setFormData({
        name: "",
        code: "",
        type: "Asset",
        subType: "",
        isActive: true
      });
    } catch (error) {
      alert("Error saving account: " + error);
    }
  };

  const handleEdit = (account: any) => {
    setEditingAccount(account);
    setFormData({
      name: account.name,
      code: account.code,
      type: account.type,
      subType: account.subType || "",
      isActive: account.isActive
    });
    setShowAddModal(true);
  };

  const handleDelete = async (accountId: any) => {
    if (confirm("Are you sure you want to delete this account?")) {
      try {
        await deleteAccount({ id: accountId });
      } catch (error) {
        alert("Error deleting account: " + error);
      }
    }
  };

  const accountTypes = ["Asset", "Liability", "Equity", "Revenue", "Expense"];
  const subTypes = {
    Asset: ["Current Asset", "Fixed Asset", "Inventory"],
    Liability: ["Current Liability", "Long-term Liability"],
    Equity: [],
    Revenue: ["Operating Revenue", "Non-operating Revenue"],
    Expense: ["Cost of Goods Sold", "Operating Expense", "Administrative Expense"]
  };

  if (!accounts) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Chart of Accounts</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Chart of Accounts</h1>
            <button
              type="button"
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Account
            </button>
          </div>

          {/* Accounts Table */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Code</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Account Name</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Sub Type</th>
                    <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
                    <th className="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {accounts.map((account) => (
                    <tr key={account._id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4 font-medium text-blue-600">{account.code}</td>
                      <td className="py-4 px-4 font-medium">{account.name}</td>
                      <td className="py-4 px-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          account.type === 'Asset' ? 'bg-blue-100 text-blue-800' :
                          account.type === 'Liability' ? 'bg-red-100 text-red-800' :
                          account.type === 'Equity' ? 'bg-purple-100 text-purple-800' :
                          account.type === 'Revenue' ? 'bg-green-100 text-green-800' :
                          'bg-orange-100 text-orange-800'
                        }`}>
                          {account.type}
                        </span>
                      </td>
                      <td className="py-4 px-4 text-sm text-gray-600">{account.subType || '-'}</td>
                      <td className="py-4 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          account.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {account.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="py-4 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <button
                            type="button"
                            onClick={() => handleEdit(account)}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            Edit
                          </button>
                          <button
                            type="button"
                            onClick={() => handleDelete(account._id)}
                            className="text-red-600 hover:text-red-800 text-sm font-medium"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Add/Edit Modal */}
          {showAddModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-md">
                <h3 className="text-lg font-semibold mb-4">
                  {editingAccount ? 'Edit Account' : 'Add New Account'}
                </h3>
                <form onSubmit={handleSubmit}>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Account Code
                      </label>
                      <input
                        type="text"
                        value={formData.code}
                        onChange={(e) => setFormData({...formData, code: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Account Name
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Account Type
                      </label>
                      <select
                        value={formData.type}
                        onChange={(e) => setFormData({...formData, type: e.target.value as any, subType: ""})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        {accountTypes.map(type => (
                          <option key={type} value={type}>{type}</option>
                        ))}
                      </select>
                    </div>
                    {subTypes[formData.type].length > 0 && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Sub Type
                        </label>
                        <select
                          value={formData.subType}
                          onChange={(e) => setFormData({...formData, subType: e.target.value as any})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">Select Sub Type</option>
                          {subTypes[formData.type].map(subType => (
                            <option key={subType} value={subType}>{subType}</option>
                          ))}
                        </select>
                      </div>
                    )}
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isActive"
                        checked={formData.isActive}
                        onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
                        className="mr-2"
                      />
                      <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
                        Active Account
                      </label>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      type="button"
                      onClick={() => {
                        setShowAddModal(false);
                        setEditingAccount(null);
                        setFormData({
                          name: "",
                          code: "",
                          type: "Asset",
                          subType: "",
                          isActive: true
                        });
                      }}
                      className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      {editingAccount ? 'Update' : 'Add'} Account
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

export default ChartOfAccounts;
