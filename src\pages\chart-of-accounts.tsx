import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function ChartOfAccounts() {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAccount, setEditingAccount] = useState<any>(null);
  const [formData, setFormData] = useState<{
    name: string;
    code: string;
    type: "Asset" | "Liability" | "Equity" | "Revenue" | "Expense";
    subType: "Current Asset" | "Fixed Asset" | "Inventory" | "Current Liability" | "Long-term Liability" | "Operating Revenue" | "Non-operating Revenue" | "Cost of Goods Sold" | "Operating Expense" | "Administrative Expense" | "";
  }>({
    name: "",
    code: "",
    type: "Asset",
    subType: ""
  });

  const accounts = useQuery(api.tasks.getAccounts);
  const addAccount = useMutation(api.tasks.addAccount);
  const updateAccount = useMutation(api.tasks.updateAccount);
  const deleteAccount = useMutation(api.tasks.deleteAccount);
  const addMissingAccounts = useMutation(api.tasks.addMissingCostExpenseAccounts);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const submitData = {
        name: formData.name,
        code: formData.code,
        type: formData.type,
        subType: formData.subType || undefined
      };

      if (editingAccount) {
        await updateAccount({
          id: editingAccount._id,
          ...submitData
        });
      } else {
        await addAccount(submitData);
      }
      setShowAddModal(false);
      setEditingAccount(null);
      setFormData({
        name: "",
        code: "",
        type: "Asset",
        subType: ""
      });
    } catch (error) {
      alert("Error saving account: " + error);
    }
  };

  const handleEdit = (account: any) => {
    setEditingAccount(account);
    setFormData({
      name: account.name,
      code: account.code,
      type: account.type,
      subType: account.subType || ""
    });
    setShowAddModal(true);
  };

  const handleDelete = async (accountId: any) => {
    if (confirm("Are you sure you want to delete this account?")) {
      try {
        await deleteAccount({ id: accountId });
      } catch (error) {
        alert("Error deleting account: " + error);
      }
    }
  };

  const handleAddMissingAccounts = async () => {
    if (confirm("This will add missing cost and expense accounts (500s series). Continue?")) {
      try {
        const result = await addMissingAccounts();
        alert(`Success! ${result.message}\n\nAdded accounts:\n${result.accountsAdded.join('\n')}`);
      } catch (error) {
        alert("Error adding missing accounts: " + error);
      }
    }
  };

  const accountTypes = ["Asset", "Liability", "Equity", "Revenue", "Expense"];
  const subTypes = {
    Asset: ["Current Asset", "Fixed Asset", "Inventory"],
    Liability: ["Current Liability", "Long-term Liability"],
    Equity: [],
    Revenue: ["Operating Revenue", "Non-operating Revenue"],
    Expense: ["Cost of Goods Sold", "Operating Expense", "Administrative Expense"]
  };

  // Group accounts by type and sort by code
  const groupedAccounts = accounts ? {
    Assets: accounts.filter(acc => acc.type === 'Asset').sort((a, b) => a.code.localeCompare(b.code)),
    Liabilities: accounts.filter(acc => acc.type === 'Liability').sort((a, b) => a.code.localeCompare(b.code)),
    Equity: accounts.filter(acc => acc.type === 'Equity').sort((a, b) => a.code.localeCompare(b.code)),
    Revenue: accounts.filter(acc => acc.type === 'Revenue').sort((a, b) => a.code.localeCompare(b.code)),
    Expenses: accounts.filter(acc => acc.type === 'Expense').sort((a, b) => a.code.localeCompare(b.code))
  } : null;

  if (!accounts || !groupedAccounts) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Chart of Accounts</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Chart of Accounts</h1>
              <p className="text-gray-600 mt-1">NEW JOY LEAN Enterprise - Account Structure</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                type="button"
                onClick={handleAddMissingAccounts}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors shadow-sm font-medium flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Missing Accounts
              </button>
              <button
                type="button"
                onClick={() => setShowAddModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Account
              </button>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800">Assets</h3>
              <p className="text-2xl font-bold text-blue-600">{groupedAccounts.Assets.length}</p>
              <p className="text-xs text-blue-600">100s Series</p>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-red-800">Liabilities</h3>
              <p className="text-2xl font-bold text-red-600">{groupedAccounts.Liabilities.length}</p>
              <p className="text-xs text-red-600">200s Series</p>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-purple-800">Equity</h3>
              <p className="text-2xl font-bold text-purple-600">{groupedAccounts.Equity.length}</p>
              <p className="text-xs text-purple-600">300s Series</p>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-green-800">Revenue</h3>
              <p className="text-2xl font-bold text-green-600">{groupedAccounts.Revenue.length}</p>
              <p className="text-xs text-green-600">400s Series</p>
            </div>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-orange-800">Expenses</h3>
              <p className="text-2xl font-bold text-orange-600">{groupedAccounts.Expenses.length}</p>
              <p className="text-xs text-orange-600">500s Series</p>
            </div>
          </div>

          {/* Account Sections */}
          {Object.entries(groupedAccounts).map(([type, accounts]) => {
            const config = {
              Assets: { bgColor: 'bg-blue-600', textColor: 'text-white', title: 'ASSETS (100s)' },
              Liabilities: { bgColor: 'bg-red-600', textColor: 'text-white', title: 'LIABILITIES (200s)' },
              Equity: { bgColor: 'bg-purple-600', textColor: 'text-white', title: 'CAPITAL/EQUITY (300s)' },
              Revenue: { bgColor: 'bg-green-600', textColor: 'text-white', title: 'INCOME/REVENUE (400s)' },
              Expenses: { bgColor: 'bg-orange-600', textColor: 'text-white', title: 'COST & EXPENSES (500s)' }
            }[type] || { bgColor: 'bg-gray-600', textColor: 'text-white', title: type };

            return (
              <div key={type} className="bg-white rounded-xl shadow-lg border border-gray-200 mb-6 overflow-hidden">
                <div className={`${config.bgColor} ${config.textColor} px-6 py-4`}>
                  <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold">{config.title}</h2>
                    <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm font-medium">
                      {accounts.length} accounts
                    </span>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                      <tr>
                        <th className="text-left py-4 px-6 font-semibold text-gray-700 text-sm uppercase tracking-wider border-b border-gray-200">Code</th>
                        <th className="text-left py-4 px-6 font-semibold text-gray-700 text-sm uppercase tracking-wider border-b border-gray-200">Account Name</th>
                        <th className="text-left py-4 px-6 font-semibold text-gray-700 text-sm uppercase tracking-wider border-b border-gray-200">Sub Type</th>
                        <th className="text-center py-4 px-6 font-semibold text-gray-700 text-sm uppercase tracking-wider border-b border-gray-200">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {accounts.length === 0 ? (
                        <tr>
                          <td colSpan={4} className="py-8 px-6 text-center text-gray-500">
                            <div className="flex flex-col items-center">
                              <svg className="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              <p className="text-lg font-medium text-gray-400">No accounts in this category</p>
                              <p className="text-sm text-gray-400">Add accounts to get started</p>
                            </div>
                          </td>
                        </tr>
                      ) : (
                        accounts.map((account: any, index: number) => (
                          <tr key={account._id} className={`hover:bg-blue-50 transition-all duration-200 ${
                            index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                          }`}>
                            <td className="py-4 px-6">
                              <span className="inline-flex items-center font-mono font-bold text-blue-700 bg-blue-100 px-3 py-1 rounded-full text-sm border border-blue-200">
                                {account.code}
                              </span>
                            </td>
                            <td className="py-4 px-6">
                              <div className="font-semibold text-gray-900 text-base">{account.name}</div>
                            </td>
                            <td className="py-4 px-6">
                              {account.subType ? (
                                <span className="inline-flex items-center text-sm text-gray-700 bg-gray-200 px-3 py-1 rounded-full border border-gray-300">
                                  {account.subType}
                                </span>
                              ) : (
                                <span className="inline-flex items-center text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full border border-gray-200">
                                  No Sub Type
                                </span>
                              )}
                            </td>
                            <td className="py-4 px-6 text-center">
                              <div className="flex justify-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => handleEdit(account)}
                                  className="inline-flex items-center px-3 py-2 border border-blue-300 text-blue-700 bg-blue-50 rounded-lg hover:bg-blue-100 hover:border-blue-400 transition-all duration-200 text-sm font-medium shadow-sm"
                                >
                                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                  Edit
                                </button>
                                <button
                                  type="button"
                                  onClick={() => handleDelete(account._id)}
                                  className="inline-flex items-center px-3 py-2 border border-red-300 text-red-700 bg-red-50 rounded-lg hover:bg-red-100 hover:border-red-400 transition-all duration-200 text-sm font-medium shadow-sm"
                                >
                                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                  Delete
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            );
          })}

          {/* Account Structure Reference */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mt-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Account Number Structure Reference</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Assets (100s)</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>111-123: Current Assets</li>
                  <li>• Cash, Receivables, Inventory</li>
                  <li>• Office Supplies, Prepaid Items</li>
                </ul>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="font-semibold text-red-800 mb-2">Liabilities (200s)</h4>
                <ul className="text-sm text-red-700 space-y-1">
                  <li>200-206: Current Liabilities</li>
                  <li>• Accounts Payable, Taxes</li>
                  <li>• Government Contributions</li>
                </ul>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-2">Capital (300s)</h4>
                <ul className="text-sm text-purple-700 space-y-1">
                  <li>300-302: Owner's Equity</li>
                  <li>• Capital, Withdrawals</li>
                  <li>• Income & Expense Summary</li>
                </ul>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">Income (400s)</h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>400-402: Sales Revenue</li>
                  <li>• Sales, Discounts</li>
                  <li>• Returns & Allowances</li>
                </ul>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">Cost & Expenses (500s)</h4>
                <ul className="text-sm text-orange-700 space-y-1">
                  <li>500-503: Cost of Goods Sold</li>
                  <li>504-520: Operating Expenses</li>
                  <li>• Utilities, Rent, Salaries</li>
                </ul>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-2">Philippine Standards</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• VAT: Input Tax (118), Output Tax (201)</li>
                  <li>• Gov't: SSS, Philhealth, Pag-ibig</li>
                  <li>• Withholding Tax (206)</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Add/Edit Modal */}
          {showAddModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-xl shadow-2xl p-6 w-full max-w-lg border border-gray-200">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">
                    {editingAccount ? 'Edit Account' : 'Add New Account'}
                  </h3>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      setEditingAccount(null);
                      setFormData({
                        name: "",
                        code: "",
                        type: "Asset",
                        subType: ""
                      });
                    }}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    aria-label="Close modal"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <form onSubmit={handleSubmit}>
                  <div className="space-y-5">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Account Code *
                      </label>
                      <input
                        type="text"
                        value={formData.code}
                        onChange={(e) => setFormData({...formData, code: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="e.g., 111, 200, 300"
                        required
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {formData.type === 'Asset' && '100s series (111-123)'}
                        {formData.type === 'Liability' && '200s series (200-206)'}
                        {formData.type === 'Equity' && '300s series (300-302)'}
                        {formData.type === 'Revenue' && '400s series (400-402)'}
                        {formData.type === 'Expense' && '500s series (500-520)'}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Account Name *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="e.g., Cash, Accounts Receivable, Sales"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Account Type *
                      </label>
                      <select
                        value={formData.type}
                        onChange={(e) => setFormData({...formData, type: e.target.value as any, subType: ""})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        required
                      >
                        {accountTypes.map(type => (
                          <option key={type} value={type}>{type}</option>
                        ))}
                      </select>
                    </div>

                    {subTypes[formData.type].length > 0 && (
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Sub Type
                        </label>
                        <select
                          value={formData.subType}
                          onChange={(e) => setFormData({...formData, subType: e.target.value as any})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        >
                          <option value="">Select Sub Type (Optional)</option>
                          {subTypes[formData.type].map(subType => (
                            <option key={subType} value={subType}>{subType}</option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={() => {
                        setShowAddModal(false);
                        setEditingAccount(null);
                        setFormData({
                          name: "",
                          code: "",
                          type: "Asset",
                          subType: ""
                        });
                      }}
                      className="px-6 py-3 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-all duration-200 font-medium"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                    >
                      {editingAccount ? 'Update Account' : 'Add Account'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

export default ChartOfAccounts;
